import{useState as o}from"react";import{useIsoMorphicEffect as h}from'./use-iso-morphic-effect.js';function u(e){if(e===null)return{width:0,height:0};let{width:r,height:t}=e.getBoundingClientRect();return{width:r,height:t}}function f(e,r=!1){let[t,l]=o(()=>u(e.current));return h(()=>{let i=e.current;if(!i)return;let n=new ResizeObserver(()=>{l(u(i))});return n.observe(i),()=>{n.disconnect()}},[e]),r?{width:`${t.width}px`,height:`${t.height}px`}:t}export{f as useElementSize};
