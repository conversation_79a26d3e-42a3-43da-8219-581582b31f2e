/* tslint:disable */
/* eslint-disable */
/**
* @param {string} query
* @param {any} opts
* @returns {any}
*/
export function browserslist(query: string, opts: any): any;
/**
* @param {string} value
* @param {any} opts
* @returns {any}
*/
export function mdxCompileSync(value: string, opts: any): any;
/**
* @param {string} value
* @param {any} opts
* @returns {Promise<any>}
*/
export function mdxCompile(value: string, opts: any): Promise<any>;
/**
* @param {string} s
* @param {any} opts
* @returns {any}
*/
export function minifySync(s: string, opts: any): any;
/**
* @param {string} s
* @param {any} opts
* @returns {Promise<any>}
*/
export function minify(s: string, opts: any): Promise<any>;
/**
* @param {any} s
* @param {any} opts
* @returns {any}
*/
export function transformSync(s: any, opts: any): any;
/**
* @param {any} s
* @param {any} opts
* @returns {Promise<any>}
*/
export function transform(s: any, opts: any): Promise<any>;
/**
* @param {string} s
* @param {any} opts
* @returns {any}
*/
export function parseSync(s: string, opts: any): any;
/**
* @param {string} s
* @param {any} opts
* @returns {Promise<any>}
*/
export function parse(s: string, opts: any): Promise<any>;
