"use client";import{useFocusRing as c}from"@react-aria/focus";import{useHover as A}from"@react-aria/interactions";import{Fragment as D,useMemo as f}from"react";import{useActivePress as v}from'../../hooks/use-active-press.js';import{forwardRefWithAs as I,mergeProps as y,render as P}from'../../utils/render.js';let E=D;function _(o,n){let{...s}=o,e=!1,{isFocusVisible:t,focusProps:p}=c(),{isHovered:r,hoverProps:i}=A({isDisabled:e}),{pressed:a,pressProps:T}=v({disabled:e}),l=y({ref:n},p,i,T),m=f(()=>({hover:r,focus:t,active:a}),[r,t,a]);return P({ourProps:l,theirProps:s,slot:m,defaultTag:E,name:"DataInteractive"})}let C=I(_);export{C as DataInteractive};
