"use client";import{useFocusRing as b}from"@react-aria/focus";import{useHover as c}from"@react-aria/interactions";import{useMemo as P}from"react";import{useActivePress as B}from'../../hooks/use-active-press.js';import{useDisabled as A}from'../../internal/disabled.js';import{forwardRefWithAs as F,mergeProps as g,render as _}from'../../utils/render.js';let v="button";function E(o,l){var p,a;let i=A(),{disabled:e=i||!1,...t}=o,{isFocusVisible:s,focusProps:T}=b({autoFocus:(p=o.autoFocus)!=null?p:!1}),{isHovered:n,hoverProps:f}=c({isDisabled:e}),{pressed:r,pressProps:m}=B({disabled:e}),d=g({ref:l,disabled:e||void 0,type:(a=t.type)!=null?a:"button"},T,f,m),y=P(()=>{var u;return{disabled:e,hover:n,focus:s,active:r,autofocus:(u=o.autoFocus)!=null?u:!1}},[e,n,s,r,o.autoFocus]);return _({ourProps:d,theirProps:t,slot:y,defaultTag:v,name:"Button"})}let H=F(E);export{H as Button};
