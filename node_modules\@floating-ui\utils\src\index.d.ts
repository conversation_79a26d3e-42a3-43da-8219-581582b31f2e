import type { Alignment, Axis, ClientRectObject, ElementRects, Length, Padding, Placement, Rect, Side, SideObject } from './types';
export declare const sides: Side[];
export declare const alignments: Alignment[];
export declare const placements: Placement[];
export declare const min: (...values: number[]) => number;
export declare const max: (...values: number[]) => number;
export declare const round: (x: number) => number;
export declare const floor: (x: number) => number;
export declare const createCoords: (v: number) => {
    x: number;
    y: number;
};
export declare function clamp(start: number, value: number, end: number): number;
export declare function evaluate<T, P>(value: T | ((param: P) => T), param: P): T;
export declare function getSide(placement: Placement): Side;
export declare function getAlignment(placement: Placement): Alignment | undefined;
export declare function getOppositeAxis(axis: Axis): Axis;
export declare function getAxisLength(axis: Axis): Length;
export declare function getSideAxis(placement: Placement): Axis;
export declare function getAlignmentAxis(placement: Placement): Axis;
export declare function getAlignmentSides(placement: Placement, rects: ElementRects, rtl?: boolean): [Side, Side];
export declare function getExpandedPlacements(placement: Placement): Array<Placement>;
export declare function getOppositeAlignmentPlacement<T extends string>(placement: T): T;
export declare function getOppositeAxisPlacements(placement: Placement, flipAlignment: boolean, direction: 'none' | Alignment, rtl?: boolean): Placement[];
export declare function getOppositePlacement<T extends string>(placement: T): T;
export declare function expandPaddingObject(padding: Partial<SideObject>): SideObject;
export declare function getPaddingObject(padding: Padding): SideObject;
export declare function rectToClientRect(rect: Rect): ClientRectObject;
