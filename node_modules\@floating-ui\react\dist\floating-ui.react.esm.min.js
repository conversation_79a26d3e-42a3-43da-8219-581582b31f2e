import*as e from"react";import{useLayoutEffect as t,useEffect as n,useRef as r}from"react";import{stopEvent as o,getDocument as u,isMouseLikePointerType as i,contains as c,activeElement as l,isSafari as s,isTypeableElement as a,getTarget as f,getPlatform as d,isReactEvent as m,isRootElement as v,isEventTargetWithin as p,isVirtualClick as g,isVirtualPointerEvent as y,isMac as h,getUserAgent as b}from"@floating-ui/react/utils";import{floor as E}from"@floating-ui/utils";import{platform as x,getOverflowAncestors as w,useFloating as R,offset as I,detectOverflow as k}from"@floating-ui/react-dom";export{arrow,autoPlacement,autoUpdate,computePosition,detectOverflow,flip,getOverflowAncestors,hide,inline,limitShift,offset,platform,shift,size}from"@floating-ui/react-dom";import{isElement as M,isHTMLElement as C,getWindow as T,isLastTraversableNode as O,getParentNode as L,getComputedStyle as P}from"@floating-ui/utils/dom";import{tabbable as S}from"tabbable";import{createPortal as A,flushSync as D}from"react-dom";function F(t){return e.useMemo((()=>t.every((e=>null==e))?null:e=>{t.forEach((t=>{"function"==typeof t?t(e):null!=t&&(t.current=e)}))}),t)}const N="ArrowUp",K="ArrowDown",H="ArrowLeft",B="ArrowRight";function _(e,t,n){return Math.floor(e/t)!==n}function q(e,t){return t<0||t>=e.current.length}function W(e,t){return z(e,{disabledIndices:t})}function j(e,t){return z(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function z(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:u=1}=void 0===t?{}:t;const i=e.current;let c=n;do{var l,s;c+=r?-u:u}while(c>=0&&c<=i.length-1&&(o?o.includes(c):null==i[c]||(null==(l=i[c])?void 0:l.hasAttribute("disabled"))||"true"===(null==(s=i[c])?void 0:s.getAttribute("aria-disabled"))));return c}function U(e,t){let{event:n,orientation:r,loop:u,cols:i,disabledIndices:c,minIndex:l,maxIndex:s,prevIndex:a,stopEvent:f=!1}=t,d=a;if(n.key===N){if(f&&o(n),-1===a)d=s;else if(d=z(e,{startingIndex:d,amount:i,decrement:!0,disabledIndices:c}),u&&(a-i<l||d<0)){const e=a%i,t=s%i,n=s-(t-e);d=t===e?s:t>e?n:n-i}q(e,d)&&(d=a)}if(n.key===K&&(f&&o(n),-1===a?d=l:(d=z(e,{startingIndex:a,amount:i,disabledIndices:c}),u&&a+i>s&&(d=z(e,{startingIndex:a%i-i,amount:i,disabledIndices:c}))),q(e,d)&&(d=a)),"both"===r){const t=E(a/i);n.key===B&&(f&&o(n),a%i!=i-1?(d=z(e,{startingIndex:a,disabledIndices:c}),u&&_(d,i,t)&&(d=z(e,{startingIndex:a-a%i-1,disabledIndices:c}))):u&&(d=z(e,{startingIndex:a-a%i-1,disabledIndices:c})),_(d,i,t)&&(d=a)),n.key===H&&(f&&o(n),a%i!=0?(d=z(e,{startingIndex:a,disabledIndices:c,decrement:!0}),u&&_(d,i,t)&&(d=z(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:c}))):u&&(d=z(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:c})),_(d,i,t)&&(d=a));const r=E(s/i)===t;q(e,d)&&(d=u&&r?n.key===H?s:z(e,{startingIndex:a-a%i-1,disabledIndices:c}):a)}return d}let X=0;function Y(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(X);const u=()=>null==e?void 0:e.focus({preventScroll:n});o?u():X=requestAnimationFrame(u)}var V="undefined"!=typeof document?t:n;function Z(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}const G=e.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function Q(t){let{children:n,elementsRef:r,labelsRef:o}=t;const[u,i]=e.useState((()=>new Map)),c=e.useCallback((e=>{i((t=>new Map(t).set(e,null)))}),[]),l=e.useCallback((e=>{i((t=>{const n=new Map(t);return n.delete(e),n}))}),[]);return V((()=>{const e=new Map(u);Array.from(e.keys()).sort(Z).forEach(((t,n)=>{e.set(t,n)})),function(e,t){if(e.size!==t.size)return!1;for(const[n,r]of e.entries())if(r!==t.get(n))return!1;return!0}(u,e)||i(e)}),[u]),e.createElement(G.Provider,{value:e.useMemo((()=>({register:c,unregister:l,map:u,elementsRef:r,labelsRef:o})),[c,l,u,r,o])},n)}function J(t){let{label:n}=void 0===t?{}:t;const[r,o]=e.useState(null),u=e.useRef(null),{register:i,unregister:c,map:l,elementsRef:s,labelsRef:a}=e.useContext(G),f=e.useCallback((e=>{if(u.current=e,null!==r&&(s.current[r]=e,a)){var t;const o=void 0!==n;a.current[r]=o?n:null!=(t=null==e?void 0:e.textContent)?t:null}}),[r,s,a,n]);return V((()=>{const e=u.current;if(e)return i(e),()=>{c(e)}}),[i,c]),V((()=>{const e=u.current?l.get(u.current):null;null!=e&&o(e)}),[l]),e.useMemo((()=>({ref:f,index:null==r?-1:r})),[r,f])}function $(t,n){return"function"==typeof t?t(n):t?e.cloneElement(t,n):e.createElement("div",n)}const ee=e.createContext({activeIndex:0,setActiveIndex:()=>{}}),te=[H,B],ne=[N,K],re=[...te,...ne],oe=e.forwardRef((function(t,n){let{render:r,orientation:o="both",loop:u=!0,cols:i=1,disabledIndices:c,...l}=t;const[s,a]=e.useState(0),f=e.useRef([]),d=r&&"function"!=typeof r?r.props:{},m=e.useMemo((()=>({activeIndex:s,setActiveIndex:a})),[s]),v=i>1;const p={...l,...d,ref:n,"aria-orientation":"both"===o?void 0:o,onKeyDown(e){null==l.onKeyDown||l.onKeyDown(e),null==d.onKeyDown||d.onKeyDown(e),function(e){if(!re.includes(e.key))return;const t=W(f,c),n=j(f,c);let r=s;v&&(r=U(f,{event:e,orientation:o,loop:u,cols:i,disabledIndices:c,minIndex:t,maxIndex:n,prevIndex:s}));const l={horizontal:[B],vertical:[K],both:[B,K]}[o],d={horizontal:[H],vertical:[N],both:[H,N]}[o],m=v?re:{horizontal:te,vertical:ne,both:re}[o];r===s&&[...l,...d].includes(e.key)&&(r=u&&r===n&&l.includes(e.key)?t:u&&r===t&&d.includes(e.key)?n:z(f,{startingIndex:r,decrement:d.includes(e.key),disabledIndices:c})),r===s||q(f,r)||(e.stopPropagation(),m.includes(e.key)&&e.preventDefault(),a(r),queueMicrotask((()=>{Y(f.current[r])})))}(e)}};return e.createElement(ee.Provider,{value:m},e.createElement(Q,{elementsRef:f},$(r,p)))})),ue=e.forwardRef((function(t,n){let{render:r,...o}=t;const u=r&&"function"!=typeof r?r.props:{},{activeIndex:i,setActiveIndex:c}=e.useContext(ee),{ref:l,index:s}=J(),a=F([l,n,u.ref]),f=i===s;return $(r,{...o,...u,ref:a,tabIndex:f?0:-1,"data-active":f?"":void 0,onFocus(e){null==o.onFocus||o.onFocus(e),null==u.onFocus||u.onFocus(e),c(s)}})}));function ie(){return ie=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ie.apply(this,arguments)}let ce=!1,le=0;const se=()=>"floating-ui-"+le++;const ae=e["useId".toString()]||function(){const[t,n]=e.useState((()=>ce?se():void 0));return V((()=>{null==t&&n(se())}),[]),e.useEffect((()=>{ce||(ce=!0)}),[]),t},fe=e.forwardRef((function(t,n){let{context:{placement:r,elements:{floating:o},middlewareData:{arrow:u}},width:i=14,height:c=7,tipRadius:l=0,strokeWidth:s=0,staticOffset:a,stroke:f,d:d,style:{transform:m,...v}={},...p}=t;const g=ae();if(!o)return null;s*=2;const y=s/2,h=i/2*(l/-8+1),b=c/2*l/4,[E,w]=r.split("-"),R=x.isRTL(o),I=!!d,k="top"===E||"bottom"===E,M=a&&"end"===w?"bottom":"top";let C=a&&"end"===w?"right":"left";a&&R&&(C="end"===w?"left":"right");const T=null!=(null==u?void 0:u.x)?a||u.x:"",O=null!=(null==u?void 0:u.y)?a||u.y:"",L=d||"M0,0 H"+i+" L"+(i-h)+","+(c-b)+" Q"+i/2+","+c+" "+h+","+(c-b)+" Z",P={top:I?"rotate(180deg)":"",left:I?"rotate(90deg)":"rotate(-90deg)",bottom:I?"":"rotate(180deg)",right:I?"rotate(-90deg)":"rotate(90deg)"}[E];return e.createElement("svg",ie({},p,{"aria-hidden":!0,ref:n,width:I?i:i+s,height:i,viewBox:"0 0 "+i+" "+(c>i?c:i),style:{position:"absolute",pointerEvents:"none",[C]:T,[M]:O,[E]:k||I?"100%":"calc(100% - "+s/2+"px)",transform:""+P+(null!=m?m:""),...v}}),s>0&&e.createElement("path",{clipPath:"url(#"+g+")",fill:"none",stroke:f,strokeWidth:s+(d?0:1),d:L}),e.createElement("path",{stroke:s&&!d?p.fill:"none",d:L}),e.createElement("clipPath",{id:g},e.createElement("rect",{x:-y,y:y*(I?-1:1),width:i+s,height:i})))}));function de(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((e=>e!==n)))||[])}}}const me=e.createContext(null),ve=e.createContext(null),pe=()=>{var t;return(null==(t=e.useContext(me))?void 0:t.id)||null},ge=()=>e.useContext(ve);function ye(e){const t=ae(),n=ge(),r=pe(),o=e||r;return V((()=>{const e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}}),[n,t,o]),t}function he(t){let{children:n,id:r}=t;const o=pe();return e.createElement(me.Provider,{value:e.useMemo((()=>({id:r,parentId:o})),[r,o])},n)}function be(t){let{children:n}=t;const r=e.useRef([]),o=e.useCallback((e=>{r.current=[...r.current,e]}),[]),u=e.useCallback((e=>{r.current=r.current.filter((t=>t!==e))}),[]),i=e.useState((()=>de()))[0];return e.createElement(ve.Provider,{value:e.useMemo((()=>({nodesRef:r,addNode:o,removeNode:u,events:i})),[r,o,u,i])},n)}function Ee(e){return"data-floating-ui-"+e}function xe(e){const t=r(e);return V((()=>{t.current=e})),t}const we=Ee("safe-polygon");function Re(e,t,n){return n&&!i(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}function Ie(t,n){void 0===n&&(n={});const{open:r,onOpenChange:o,dataRef:l,events:s,elements:{domReference:a,floating:f},refs:d}=t,{enabled:m=!0,delay:v=0,handleClose:p=null,mouseOnly:g=!1,restMs:y=0,move:h=!0}=n,b=ge(),E=pe(),x=xe(p),w=xe(v),R=e.useRef(),I=e.useRef(),k=e.useRef(),C=e.useRef(),T=e.useRef(!0),O=e.useRef(!1),L=e.useRef((()=>{})),P=e.useCallback((()=>{var e;const t=null==(e=l.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}),[l]);e.useEffect((()=>{if(m)return s.on("dismiss",e),()=>{s.off("dismiss",e)};function e(){clearTimeout(I.current),clearTimeout(C.current),T.current=!0}}),[m,s]),e.useEffect((()=>{if(!m||!x.current||!r)return;function e(e){P()&&o(!1,e)}const t=u(f).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[f,r,o,m,x,l,P]);const S=e.useCallback((function(e,t){void 0===t&&(t=!0);const n=Re(w.current,"close",R.current);n&&!k.current?(clearTimeout(I.current),I.current=setTimeout((()=>o(!1,e)),n)):t&&(clearTimeout(I.current),o(!1,e))}),[w,o]),A=e.useCallback((()=>{L.current(),k.current=void 0}),[]),D=e.useCallback((()=>{if(O.current){const e=u(d.floating.current).body;e.style.pointerEvents="",e.removeAttribute(we),O.current=!1}}),[d]);return e.useEffect((()=>{if(m&&M(a)){const e=a;return r&&e.addEventListener("mouseleave",d),null==f||f.addEventListener("mouseleave",d),h&&e.addEventListener("mousemove",n,{once:!0}),e.addEventListener("mouseenter",n),e.addEventListener("mouseleave",s),()=>{r&&e.removeEventListener("mouseleave",d),null==f||f.removeEventListener("mouseleave",d),h&&e.removeEventListener("mousemove",n),e.removeEventListener("mouseenter",n),e.removeEventListener("mouseleave",s)}}function e(){return!!l.current.openEvent&&["click","mousedown"].includes(l.current.openEvent.type)}function n(e){if(clearTimeout(I.current),T.current=!1,g&&!i(R.current)||y>0&&0===Re(w.current,"open"))return;const t=Re(w.current,"open",R.current);t?I.current=setTimeout((()=>{o(!0,e)}),t):o(!0,e)}function s(n){if(e())return;L.current();const o=u(f);if(clearTimeout(C.current),x.current){r||clearTimeout(I.current),k.current=x.current({...t,tree:b,x:n.clientX,y:n.clientY,onClose(){D(),A(),S(n)}});const e=k.current;return o.addEventListener("mousemove",e),void(L.current=()=>{o.removeEventListener("mousemove",e)})}("touch"!==R.current||!c(f,n.relatedTarget))&&S(n)}function d(n){e()||null==x.current||x.current({...t,tree:b,x:n.clientX,y:n.clientY,onClose(){D(),A(),S(n)}})(n)}}),[a,f,m,t,g,y,h,S,A,D,o,r,b,w,x,l]),V((()=>{var e;if(m&&r&&null!=(e=x.current)&&e.__options.blockPointerEvents&&P()){const e=u(f).body;if(e.setAttribute(we,""),e.style.pointerEvents="none",O.current=!0,M(a)&&f){var t,n;const e=a,r=null==b||null==(t=b.nodesRef.current.find((e=>e.id===E)))||null==(n=t.context)?void 0:n.elements.floating;return r&&(r.style.pointerEvents=""),e.style.pointerEvents="auto",f.style.pointerEvents="auto",()=>{e.style.pointerEvents="",f.style.pointerEvents=""}}}}),[m,r,E,f,a,b,x,l,P]),V((()=>{r||(R.current=void 0,A(),D())}),[r,A,D]),e.useEffect((()=>()=>{A(),clearTimeout(I.current),clearTimeout(C.current),D()}),[m,a,A,D]),e.useMemo((()=>{if(!m)return{};function e(e){R.current=e.pointerType}return{reference:{onPointerDown:e,onPointerEnter:e,onMouseMove(e){r||0===y||(clearTimeout(C.current),C.current=setTimeout((()=>{T.current||o(!0,e.nativeEvent)}),y))}},floating:{onMouseEnter(){clearTimeout(I.current)},onMouseLeave(e){s.emit("dismiss",{type:"mouseLeave",data:{returnFocus:!1}}),S(e.nativeEvent,!1)}}}}),[s,m,y,r,o,S])}const ke=e.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:()=>{},setState:()=>{},isInstantPhase:!1}),Me=()=>e.useContext(ke),Ce=t=>{let{children:n,delay:r,timeoutMs:o=0}=t;const[u,i]=e.useReducer(((e,t)=>({...e,...t})),{delay:r,timeoutMs:o,initialDelay:r,currentId:null,isInstantPhase:!1}),c=e.useRef(null),l=e.useCallback((e=>{i({currentId:e})}),[]);return V((()=>{u.currentId?null===c.current?c.current=u.currentId:i({isInstantPhase:!0}):(i({isInstantPhase:!1}),c.current=null)}),[u.currentId]),e.createElement(ke.Provider,{value:e.useMemo((()=>({...u,setState:i,setCurrentId:l})),[u,i,l])},n)},Te=(e,t)=>{let{open:n,onOpenChange:r}=e,{id:o}=t;const{currentId:u,setCurrentId:i,initialDelay:c,setState:l,timeoutMs:s}=Me();V((()=>{u&&(l({delay:{open:1,close:Re(c,"close")}}),u!==o&&r(!1))}),[o,r,l,u,c]),V((()=>{function e(){r(!1),l({delay:c,currentId:null})}if(!n&&u===o){if(s){const t=window.setTimeout(e,s);return()=>{clearTimeout(t)}}e()}}),[n,l,u,o,r,c,s]),V((()=>{n&&i(o)}),[n,i,o])};function Oe(e,t){let n=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),r=n;for(;r.length;)r=e.filter((e=>{var t;return null==(t=r)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})),n=n.concat(r);return n}let Le=new WeakMap,Pe=new WeakSet,Se={},Ae=0;const De=e=>e&&(e.host||De(e.parentNode)),Fe=(e,t)=>t.map((t=>{if(e.contains(t))return t;const n=De(t);return e.contains(n)?n:null})).filter((e=>null!=e));function Ne(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=u(e[0]).body;return function(e,t,n,r){const o="data-floating-ui-inert",u=r?"inert":n?"aria-hidden":null,i=Fe(t,e),c=new Set,l=new Set(i),s=[];Se[o]||(Se[o]=new WeakMap);const a=Se[o];return i.forEach((function e(t){t&&!c.has(t)&&(c.add(t),t.parentNode&&e(t.parentNode))})),function e(t){t&&!l.has(t)&&Array.prototype.forEach.call(t.children,(t=>{if(c.has(t))e(t);else{const e=u?t.getAttribute(u):null,n=null!==e&&"false"!==e,r=(Le.get(t)||0)+1,i=(a.get(t)||0)+1;Le.set(t,r),a.set(t,i),s.push(t),1===r&&n&&Pe.add(t),1===i&&t.setAttribute(o,""),!n&&u&&t.setAttribute(u,"true")}}))}(t),c.clear(),Ae++,()=>{s.forEach((e=>{const t=(Le.get(e)||0)-1,n=(a.get(e)||0)-1;Le.set(e,t),a.set(e,n),t||(!Pe.has(e)&&u&&e.removeAttribute(u),Pe.delete(e)),n||e.removeAttribute(o)})),Ae--,Ae||(Le=new WeakMap,Le=new WeakMap,Pe=new WeakSet,Se={})}}(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}const Ke=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function He(e,t){const n=S(e,Ke());"prev"===t&&n.reverse();const r=n.indexOf(l(u(e)));return n.slice(r+1)[0]}function Be(){return He(document.body,"next")}function _e(){return He(document.body,"prev")}function qe(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!c(n,r)}function We(e){S(e,Ke()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function je(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}const ze={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0};function Ue(e){"Tab"===e.key&&(e.target,clearTimeout(undefined))}const Xe=e.forwardRef((function(t,n){const[r,o]=e.useState();V((()=>(s()&&o("button"),document.addEventListener("keydown",Ue),()=>{document.removeEventListener("keydown",Ue)})),[]);const u={ref:n,tabIndex:0,role:r,"aria-hidden":!r||void 0,[Ee("focus-guard")]:"",style:ze};return e.createElement("span",ie({},t,u))})),Ye=e.createContext(null);function Ve(t){let{id:n,root:r}=void 0===t?{}:t;const[o,u]=e.useState(null),i=ae(),c=Ge(),l=e.useMemo((()=>({id:n,root:r,portalContext:c,uniqueId:i})),[n,r,c,i]),s=e.useRef();return V((()=>()=>{null==o||o.remove()}),[o,l]),V((()=>{if(s.current===l)return;s.current=l;const{id:e,root:t,portalContext:n,uniqueId:r}=l,o=e?document.getElementById(e):null,i=Ee("portal");if(o){const e=document.createElement("div");e.id=r,e.setAttribute(i,""),o.appendChild(e),u(e)}else{let o=t||(null==n?void 0:n.portalNode);o&&!M(o)&&(o=o.current),o=o||document.body;let c=null;e&&(c=document.createElement("div"),c.id=e,o.appendChild(c));const l=document.createElement("div");l.id=r,l.setAttribute(i,""),o=c||o,o.appendChild(l),u(l)}}),[l]),o}function Ze(t){let{children:n,id:r,root:o=null,preserveTabOrder:u=!0}=t;const i=Ve({id:r,root:o}),[c,l]=e.useState(null),s=e.useRef(null),a=e.useRef(null),f=e.useRef(null),d=e.useRef(null),m=!!c&&!c.modal&&c.open&&u&&!(!o&&!i);return e.useEffect((()=>{if(i&&u&&(null==c||!c.modal))return i.addEventListener("focusin",e,!0),i.addEventListener("focusout",e,!0),()=>{i.removeEventListener("focusin",e,!0),i.removeEventListener("focusout",e,!0)};function e(e){if(i&&qe(e)){("focusin"===e.type?je:We)(i)}}}),[i,u,null==c?void 0:c.modal]),e.createElement(Ye.Provider,{value:e.useMemo((()=>({preserveTabOrder:u,beforeOutsideRef:s,afterOutsideRef:a,beforeInsideRef:f,afterInsideRef:d,portalNode:i,setFocusManagerState:l})),[u,i])},m&&i&&e.createElement(Xe,{"data-type":"outside",ref:s,onFocus:e=>{if(qe(e,i)){var t;null==(t=f.current)||t.focus()}else{const e=_e()||(null==c?void 0:c.refs.domReference.current);null==e||e.focus()}}}),m&&i&&e.createElement("span",{"aria-owns":i.id,style:ze}),i&&A(n,i),m&&i&&e.createElement(Xe,{"data-type":"outside",ref:a,onFocus:e=>{if(qe(e,i)){var t;null==(t=d.current)||t.focus()}else{const t=Be()||(null==c?void 0:c.refs.domReference.current);null==t||t.focus(),(null==c?void 0:c.closeOnFocusOut)&&(null==c||c.onOpenChange(!1,e.nativeEvent))}}}))}const Ge=()=>e.useContext(Ye),Qe=e.forwardRef((function(t,n){return e.createElement("button",ie({},t,{type:"button",ref:n,tabIndex:-1,style:ze}))}));function Je(t){const{context:n,children:r,disabled:i=!1,order:s=["content"],guards:d=!0,initialFocus:m=0,returnFocus:v=!0,modal:p=!0,visuallyHiddenDismiss:g=!1,closeOnFocusOut:y=!0}=t,{open:h,refs:b,nodeId:E,onOpenChange:x,events:w,dataRef:R,elements:{domReference:I,floating:k}}=n,M="number"==typeof m&&m<0,T="combobox"===(null==I?void 0:I.getAttribute("role"))&&a(I)&&M,O=!T&&p,L="undefined"==typeof HTMLElement||!("inert"in HTMLElement.prototype)||d,P=xe(s),A=xe(m),D=xe(v),F=ge(),N=Ge(),K=e.useRef(null),H=e.useRef(null),B=e.useRef(!1),_=e.useRef(null),q=e.useRef(!1),W=null!=N,j=e.useCallback((function(e){return void 0===e&&(e=k),e?S(e,Ke()):[]}),[k]),z=e.useCallback((e=>{const t=j(e);return P.current.map((e=>I&&"reference"===e?I:k&&"floating"===e?k:t)).filter(Boolean).flat()}),[I,k,P,j]);function U(t){return!i&&g&&O?e.createElement(Qe,{ref:"start"===t?K:H,onClick:e=>x(!1,e.nativeEvent)},"string"==typeof g?g:"Dismiss"):null}e.useEffect((()=>{if(i||!O)return;function e(e){if("Tab"===e.key){c(k,l(u(k)))&&0===j().length&&!T&&o(e);const t=z(),n=f(e);"reference"===P.current[0]&&n===I&&(o(e),e.shiftKey?Y(t[t.length-1]):Y(t[1])),"floating"===P.current[1]&&n===k&&e.shiftKey&&(o(e),Y(t[0]))}}const t=u(k);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}}),[i,I,k,O,P,b,T,j,z]),e.useEffect((()=>{if(!i&&y)return k&&C(I)?(I.addEventListener("focusout",t),I.addEventListener("pointerdown",e),!O&&k.addEventListener("focusout",t),()=>{I.removeEventListener("focusout",t),I.removeEventListener("pointerdown",e),!O&&k.removeEventListener("focusout",t)}):void 0;function e(){q.current=!0,setTimeout((()=>{q.current=!1}))}function t(e){const t=e.relatedTarget;queueMicrotask((()=>{const n=!(c(I,t)||c(k,t)||c(t,k)||c(null==N?void 0:N.portalNode,t)||null!=t&&t.hasAttribute(Ee("focus-guard"))||F&&(Oe(F.nodesRef.current,E).find((e=>{var n,r;return c(null==(n=e.context)?void 0:n.elements.floating,t)||c(null==(r=e.context)?void 0:r.elements.domReference,t)}))||function(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}(F.nodesRef.current,E).find((e=>{var n,r;return(null==(n=e.context)?void 0:n.elements.floating)===t||(null==(r=e.context)?void 0:r.elements.domReference)===t}))));t&&n&&!q.current&&t!==_.current&&(B.current=!0,x(!1,e))}))}}),[i,I,k,O,E,F,N,x,y]),e.useEffect((()=>{var e;if(i)return;const t=Array.from((null==N||null==(e=N.portalNode)?void 0:e.querySelectorAll("["+Ee("portal")+"]"))||[]);if(k){const e=[k,...t,K.current,H.current,P.current.includes("reference")||T?I:null].filter((e=>null!=e)),n=p||T?Ne(e,L,!L):Ne(e);return()=>{n()}}}),[i,I,k,p,P,N,T,L]),V((()=>{if(i||!k)return;const e=u(k),t=l(e);queueMicrotask((()=>{const e=z(k),n=A.current,r=("number"==typeof n?e[n]:n.current)||k,o=c(k,t);M||o||!h||Y(r,{preventScroll:r===k})}))}),[i,h,k,M,z,A]),V((()=>{if(i||!k)return;let e=!1;const t=u(k),n=l(t),r=R.current;function o(t){if("escapeKey"===t.type&&b.domReference.current&&(_.current=b.domReference.current),["referencePress","escapeKey"].includes(t.type))return;const n=t.data.returnFocus;"object"==typeof n?(B.current=!1,e=n.preventScroll):B.current=!n}return _.current=n,w.on("dismiss",o),()=>{w.off("dismiss",o);const n=l(t);(c(k,n)||F&&Oe(F.nodesRef.current,E).some((e=>{var t;return c(null==(t=e.context)?void 0:t.elements.floating,n)}))||r.openEvent&&["click","mousedown"].includes(r.openEvent.type))&&b.domReference.current&&(_.current=b.domReference.current),D.current&&C(_.current)&&!B.current&&Y(_.current,{cancelPrevious:!1,preventScroll:e})}}),[i,k,D,R,b,w,F,E]),V((()=>{if(!i&&N)return N.setFocusManagerState({modal:O,closeOnFocusOut:y,open:h,onOpenChange:x,refs:b}),()=>{N.setFocusManagerState(null)}}),[i,N,O,h,x,b,y]),V((()=>{if(i||!k||"function"!=typeof MutationObserver||M)return;const e=()=>{const e=k.getAttribute("tabindex");P.current.includes("floating")||l(u(k))!==b.domReference.current&&0===j().length?"0"!==e&&k.setAttribute("tabindex","0"):"-1"!==e&&k.setAttribute("tabindex","-1")};e();const t=new MutationObserver(e);return t.observe(k,{childList:!0,subtree:!0,attributes:!0}),()=>{t.disconnect()}}),[i,k,b,P,j,M]);const X=!i&&L&&(W||O);return e.createElement(e.Fragment,null,X&&e.createElement(Xe,{"data-type":"inside",ref:null==N?void 0:N.beforeInsideRef,onFocus:e=>{if(O){const e=z();Y("reference"===s[0]?e[0]:e[e.length-1])}else if(null!=N&&N.preserveTabOrder&&N.portalNode)if(B.current=!1,qe(e,N.portalNode)){const e=Be()||I;null==e||e.focus()}else{var t;null==(t=N.beforeOutsideRef.current)||t.focus()}}}),!T&&U("start"),r,U("end"),X&&e.createElement(Xe,{"data-type":"inside",ref:null==N?void 0:N.afterInsideRef,onFocus:e=>{if(O)Y(z()[0]);else if(null!=N&&N.preserveTabOrder&&N.portalNode)if(y&&(B.current=!0),qe(e,N.portalNode)){const e=_e()||I;null==e||e.focus()}else{var t;null==(t=N.afterOutsideRef.current)||t.focus()}}}))}const $e=new Set,et=e.forwardRef((function(t,n){let{lockScroll:r=!1,...o}=t;const u=ae();return V((()=>{if(!r)return;$e.add(u);const e=/iP(hone|ad|od)|iOS/.test(d()),t=document.body.style,n=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",o=window.innerWidth-document.documentElement.clientWidth,i=t.left?parseFloat(t.left):window.pageXOffset,c=t.top?parseFloat(t.top):window.pageYOffset;if(t.overflow="hidden",o&&(t[n]=o+"px"),e){var l,s;const e=(null==(l=window.visualViewport)?void 0:l.offsetLeft)||0,n=(null==(s=window.visualViewport)?void 0:s.offsetTop)||0;Object.assign(t,{position:"fixed",top:-(c-Math.floor(n))+"px",left:-(i-Math.floor(e))+"px",right:"0"})}return()=>{$e.delete(u),0===$e.size&&(Object.assign(t,{overflow:"",[n]:""}),e&&(Object.assign(t,{position:"",top:"",left:"",right:""}),window.scrollTo(i,c)))}}),[u,r]),e.createElement("div",ie({ref:n},o,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...o.style}}))}));function tt(e){return C(e.target)&&"BUTTON"===e.target.tagName}function nt(e){return a(e)}function rt(t,n){void 0===n&&(n={});const{open:r,onOpenChange:o,dataRef:u,elements:{domReference:c}}=t,{enabled:l=!0,event:s="click",toggle:a=!0,ignoreMouse:f=!1,keyboardHandlers:d=!0}=n,m=e.useRef(),v=e.useRef(!1);return e.useMemo((()=>l?{reference:{onPointerDown(e){m.current=e.pointerType},onMouseDown(e){0===e.button&&(i(m.current,!0)&&f||"click"!==s&&(!r||!a||u.current.openEvent&&"mousedown"!==u.current.openEvent.type?(e.preventDefault(),o(!0,e.nativeEvent)):o(!1,e.nativeEvent)))},onClick(e){"mousedown"===s&&m.current?m.current=void 0:i(m.current,!0)&&f||(!r||!a||u.current.openEvent&&"click"!==u.current.openEvent.type?o(!0,e.nativeEvent):o(!1,e.nativeEvent))},onKeyDown(e){m.current=void 0,e.defaultPrevented||!d||tt(e)||(" "!==e.key||nt(c)||(e.preventDefault(),v.current=!0),"Enter"===e.key&&o(!r||!a,e.nativeEvent))},onKeyUp(e){e.defaultPrevented||!d||tt(e)||nt(c)||" "===e.key&&v.current&&(v.current=!1,o(!r||!a,e.nativeEvent))}}}:{}),[l,u,s,f,d,c,a,r,o])}const ot=e["useInsertionEffect".toString()]||(e=>e());function ut(t){const n=e.useRef((()=>{}));return ot((()=>{n.current=t})),e.useCallback((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)}),[])}function it(e){return null!=e&&null!=e.clientX}function ct(t,n){void 0===n&&(n={});const{open:r,refs:o,dataRef:u,elements:{floating:l}}=t,{enabled:s=!0,axis:a="both",x:d=null,y:m=null}=n,v=e.useRef(!1),p=e.useRef(null),[g,y]=e.useState(),[h,b]=e.useState([]),E=ut(((e,t)=>{v.current||u.current.openEvent&&!it(u.current.openEvent)||o.setPositionReference(function(e,t){let n=null,r=null,o=!1;return{contextElement:e.current||void 0,getBoundingClientRect(){var u,i;const c=(null==(u=e.current)?void 0:u.getBoundingClientRect())||{width:0,height:0,x:0,y:0},l="x"===t.axis||"both"===t.axis,s="y"===t.axis||"both"===t.axis,a=["mouseenter","mousemove"].includes((null==(i=t.dataRef.current.openEvent)?void 0:i.type)||"")&&"touch"!==t.pointerType;let f=c.width,d=c.height,m=c.x,v=c.y;return null==n&&t.x&&l&&(n=c.x-t.x),null==r&&t.y&&s&&(r=c.y-t.y),m-=n||0,v-=r||0,f=0,d=0,!o||a?(f="y"===t.axis?c.width:0,d="x"===t.axis?c.height:0,m=l&&null!=t.x?t.x:m,v=s&&null!=t.y?t.y:v):o&&!a&&(d="x"===t.axis?c.height:d,f="y"===t.axis?c.width:f),o=!0,{width:f,height:d,x:m,y:v,top:v,right:m+f,bottom:v+d,left:m}}}}(o.domReference,{x:e,y:t,axis:a,dataRef:u,pointerType:g}))})),x=ut((e=>{null==d&&null==m&&(r?p.current||b([]):E(e.clientX,e.clientY))})),w=i(g)?l:r,R=e.useCallback((()=>{if(!w||!s||null!=d||null!=m)return;const e=T(o.floating.current);function t(n){const r=f(n);c(o.floating.current,r)?(e.removeEventListener("mousemove",t),p.current=null):E(n.clientX,n.clientY)}if(!u.current.openEvent||it(u.current.openEvent)){e.addEventListener("mousemove",t);const n=()=>{e.removeEventListener("mousemove",t),p.current=null};return p.current=n,n}o.setPositionReference(o.domReference.current)}),[u,s,w,o,E,d,m]);return e.useEffect((()=>R()),[R,h]),e.useEffect((()=>{s&&!l&&(v.current=!1)}),[s,l]),e.useEffect((()=>{!s&&r&&(v.current=!0)}),[s,r]),V((()=>{!s||null==d&&null==m||(v.current=!1,E(d,m))}),[s,d,m,E]),e.useMemo((()=>{if(!s)return{};function e(e){let{pointerType:t}=e;y(t)}return{reference:{onPointerDown:e,onPointerEnter:e,onMouseMove:x,onMouseEnter:x}}}),[s,x])}const lt={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},st={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"};function at(t,n){void 0===n&&(n={});const{open:r,onOpenChange:o,events:i,nodeId:l,elements:{reference:s,domReference:a,floating:d},dataRef:h}=t,{enabled:b=!0,escapeKey:E=!0,outsidePress:x=!0,outsidePressEvent:R="pointerdown",referencePress:I=!1,referencePressEvent:k="pointerdown",ancestorScroll:T=!1,bubbles:S}=n,A=ge(),D=null!=pe(),F=ut("function"==typeof x?x:()=>!1),N="function"==typeof x?F:x,K=e.useRef(!1),H=e.useRef(!1),{escapeKeyBubbles:B,outsidePressBubbles:_}=(e=>{var t,n;return{escapeKeyBubbles:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePressBubbles:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}})(S),q=ut((e=>{if(!r||!b||!E||"Escape"!==e.key)return;const t=A?Oe(A.nodesRef.current,l):[];if(!B&&(e.stopPropagation(),t.length>0)){let e=!0;if(t.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)})),!e)return}i.emit("dismiss",{type:"escapeKey",data:{returnFocus:{preventScroll:!1}}}),o(!1,m(e)?e.nativeEvent:e)})),W=ut((e=>{const t=K.current;K.current=!1;const n=H.current;if(H.current=!1,"click"===R&&n)return;if(t)return;if("function"==typeof N&&!N(e))return;const r=f(e),s="["+Ee("inert")+"]",m=u(d).querySelectorAll(s);let h=M(r)?r:null;for(;h&&!O(h);){const e=L(h);if(e===u(d).body||!M(e))break;h=e}if(m.length&&M(r)&&!v(r)&&!c(r,d)&&Array.from(m).every((e=>!c(h,e))))return;if(C(r)&&d){const t=r.clientWidth>0&&r.scrollWidth>r.clientWidth,n=r.clientHeight>0&&r.scrollHeight>r.clientHeight;let o=n&&e.offsetX>r.clientWidth;if(n){"rtl"===P(r).direction&&(o=e.offsetX<=r.offsetWidth-r.clientWidth)}if(o||t&&e.offsetY>r.clientHeight)return}const b=A&&Oe(A.nodesRef.current,l).some((t=>{var n;return p(e,null==(n=t.context)?void 0:n.elements.floating)}));if(p(e,d)||p(e,a)||b)return;const E=A?Oe(A.nodesRef.current,l):[];if(E.length>0){let e=!0;if(E.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)})),!e)return}i.emit("dismiss",{type:"outsidePress",data:{returnFocus:D?{preventScroll:!0}:g(e)||y(e)}}),o(!1,e)}));return e.useEffect((()=>{if(!r||!b)return;function e(e){o(!1,e)}h.current.__escapeKeyBubbles=B,h.current.__outsidePressBubbles=_;const t=u(d);E&&t.addEventListener("keydown",q),N&&t.addEventListener(R,W);let n=[];return T&&(M(a)&&(n=w(a)),M(d)&&(n=n.concat(w(d))),!M(s)&&s&&s.contextElement&&(n=n.concat(w(s.contextElement)))),n=n.filter((e=>{var n;return e!==(null==(n=t.defaultView)?void 0:n.visualViewport)})),n.forEach((t=>{t.addEventListener("scroll",e,{passive:!0})})),()=>{E&&t.removeEventListener("keydown",q),N&&t.removeEventListener(R,W),n.forEach((t=>{t.removeEventListener("scroll",e)}))}}),[h,d,a,s,E,N,R,r,o,T,b,B,_,q,W]),e.useEffect((()=>{K.current=!1}),[N,R]),e.useMemo((()=>b?{reference:{onKeyDown:q,[lt[k]]:e=>{I&&(i.emit("dismiss",{type:"referencePress",data:{returnFocus:!1}}),o(!1,e.nativeEvent))}},floating:{onKeyDown:q,onMouseDown(){H.current=!0},onMouseUp(){H.current=!0},[st[R]]:()=>{K.current=!0}}}:{}),[b,i,I,R,k,o,q])}function ft(t){var n;void 0===t&&(t={});const{open:r=!1,onOpenChange:o,nodeId:u}=t,[i,c]=e.useState(null),l=(null==(n=t.elements)?void 0:n.reference)||i,s=R(t),a=ge(),f=ut(((e,t)=>{e&&(m.current.openEvent=t),null==o||o(e,t)})),d=e.useRef(null),m=e.useRef({}),v=e.useState((()=>de()))[0],p=ae(),g=e.useCallback((e=>{const t=M(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;s.refs.setReference(t)}),[s.refs]),y=e.useCallback((e=>{(M(e)||null===e)&&(d.current=e,c(e)),(M(s.refs.reference.current)||null===s.refs.reference.current||null!==e&&!M(e))&&s.refs.setReference(e)}),[s.refs]),h=e.useMemo((()=>({...s.refs,setReference:y,setPositionReference:g,domReference:d})),[s.refs,y,g]),b=e.useMemo((()=>({...s.elements,domReference:l})),[s.elements,l]),E=e.useMemo((()=>({...s,refs:h,elements:b,dataRef:m,nodeId:u,floatingId:p,events:v,open:r,onOpenChange:f})),[s,u,p,v,r,f,h,b]);return V((()=>{const e=null==a?void 0:a.nodesRef.current.find((e=>e.id===u));e&&(e.context=E)})),e.useMemo((()=>({...s,context:E,refs:h,elements:b})),[s,h,b,E])}function dt(t,n){void 0===n&&(n={});const{open:r,onOpenChange:o,events:i,refs:d,elements:{floating:m,domReference:v}}=t,{enabled:p=!0,visibleOnly:g=!0}=n,y=e.useRef(!1),h=e.useRef(),b=e.useRef(!1);return e.useEffect((()=>{if(!p)return;const e=T(v);function t(){!r&&C(v)&&v===l(u(v))&&(y.current=!0)}function n(){b.current=!0}return e.addEventListener("blur",t),e.addEventListener("keydown",n,!0),()=>{e.removeEventListener("blur",t),e.removeEventListener("keydown",n,!0)}}),[m,v,r,p]),e.useEffect((()=>{if(p)return i.on("dismiss",e),()=>{i.off("dismiss",e)};function e(e){"referencePress"!==e.type&&"escapeKey"!==e.type||(y.current=!0)}}),[i,p]),e.useEffect((()=>()=>{clearTimeout(h.current)}),[]),e.useMemo((()=>p?{reference:{onPointerDown(){b.current=!1},onMouseLeave(){y.current=!1},onFocus(e){if(y.current)return;const t=f(e.nativeEvent);if(g&&M(t))try{if(s())throw Error();if(!t.matches(":focus-visible"))return}catch(e){if(!b.current&&!a(t))return}o(!0,e.nativeEvent)},onBlur(e){y.current=!1;const t=e.relatedTarget,n=M(t)&&t.hasAttribute(Ee("focus-guard"))&&"outside"===t.getAttribute("data-type");h.current=window.setTimeout((()=>{const r=l(v?v.ownerDocument:document);(t||r!==v)&&(c(d.floating.current,t)||c(v,t)||n||o(!1,e.nativeEvent))}))}}}:{}),[p,g,v,d,o])}function mt(e,t,n){const r=new Map;return{..."floating"===n&&{tabIndex:-1},...e,...t.map((e=>e?e[n]:null)).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,o]=t;var u;0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof o&&(null==(u=r.get(n))||u.push(o),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),u=0;u<t;u++)o[u]=arguments[u];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=o})),e):e),{})}}function vt(t){void 0===t&&(t=[]);const n=t,r=e.useCallback((e=>mt(e,t,"reference")),n),o=e.useCallback((e=>mt(e,t,"floating")),n),u=e.useCallback((e=>mt(e,t,"item")),t.map((e=>null==e?void 0:e.item)));return e.useMemo((()=>({getReferenceProps:r,getFloatingProps:o,getItemProps:u})),[r,o,u])}let pt=!1;function gt(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function yt(e,t){return gt(t,e===N||e===K,e===H||e===B)}function ht(e,t,n){return gt(t,e===K,n?e===H:e===B)||"Enter"===e||" "==e||""===e}function bt(e,t,n){return gt(t,n?e===B:e===H,e===N)}function Et(t,n){const{open:r,onOpenChange:i,refs:a,elements:{domReference:f,floating:d}}=t,{listRef:m,activeIndex:v,onNavigate:p=(()=>{}),enabled:b=!0,selectedIndex:E=null,allowEscape:x=!1,loop:w=!1,nested:R=!1,rtl:I=!1,virtual:k=!1,focusItemOnOpen:M="auto",focusItemOnHover:T=!0,openOnArrowKeyDown:O=!0,disabledIndices:L,orientation:P="vertical",cols:S=1,scrollItemIntoView:A=!0,virtualItemRef:D}=n,F=pe(),N=ge(),_=ut(p),X=e.useRef(M),Z=e.useRef(null!=E?E:-1),G=e.useRef(null),Q=e.useRef(!0),J=e.useRef(_),$=e.useRef(!!d),ee=e.useRef(!1),te=e.useRef(!1),ne=xe(L),re=xe(r),oe=xe(A),[ue,ie]=e.useState(),[ce,le]=e.useState(),se=ut((function(e,t,n){void 0===n&&(n=!1);const r=e.current[t.current];r&&(k?(ie(r.id),null==N||N.events.emit("virtualfocus",r),D&&(D.current=r)):Y(r,{preventScroll:!0,sync:!(!h()||!s())&&(pt||ee.current)}),requestAnimationFrame((()=>{const e=oe.current;e&&r&&(n||!Q.current)&&(null==r.scrollIntoView||r.scrollIntoView("boolean"==typeof e?{block:"nearest",inline:"nearest"}:e))})))}));V((()=>{document.createElement("div").focus({get preventScroll(){return pt=!0,!1}})}),[]),V((()=>{b&&(r&&d?X.current&&null!=E&&(te.current=!0,_(E)):$.current&&(Z.current=-1,J.current(null)))}),[b,r,d,E,_]),V((()=>{if(b&&r&&d)if(null==v){if(ee.current=!1,null!=E)return;if($.current&&(Z.current=-1,se(m,Z)),!$.current&&X.current&&(null!=G.current||!0===X.current&&null==G.current)){let e=0;const t=()=>{if(null==m.current[0]){if(e<2){(e?requestAnimationFrame:queueMicrotask)(t)}e++}else Z.current=null==G.current||ht(G.current,P,I)||R?W(m,ne.current):j(m,ne.current),G.current=null,_(Z.current)};t()}}else q(m,v)||(Z.current=v,se(m,Z,te.current),te.current=!1)}),[b,r,d,v,E,R,m,P,I,_,se,ne]),V((()=>{var e,t;if(!b||d||!N||k||!$.current)return;const n=N.nodesRef.current,r=null==(e=n.find((e=>e.id===F)))||null==(t=e.context)?void 0:t.elements.floating,o=l(u(d)),i=n.some((e=>e.context&&c(e.context.elements.floating,o)));r&&!i&&Q.current&&r.focus({preventScroll:!0})}),[b,d,N,F,k]),V((()=>{if(b&&N&&k&&!F)return N.events.on("virtualfocus",e),()=>{N.events.off("virtualfocus",e)};function e(e){le(e.id),D&&(D.current=e)}}),[b,N,k,F,D]),V((()=>{J.current=_,$.current=!!d})),V((()=>{r||(G.current=null)}),[r]);const ae=null!=v,fe=e.useMemo((()=>{function e(e){if(!r)return;const t=m.current.indexOf(e);-1!==t&&_(t)}return{onFocus(t){let{currentTarget:n}=t;e(n)},onClick:e=>{let{currentTarget:t}=e;return t.focus({preventScroll:!0})},...T&&{onMouseMove(t){let{currentTarget:n}=t;e(n)},onPointerLeave(e){let{pointerType:t}=e;Q.current&&"touch"!==t&&(Z.current=-1,se(m,Z),_(null),k||Y(a.floating.current,{preventScroll:!0}))}}}}),[r,a,se,T,m,_,k]);return e.useMemo((()=>{if(!b)return{};const e=ne.current;function t(t){if(Q.current=!1,ee.current=!0,!re.current&&t.currentTarget===a.floating.current)return;if(R&&bt(t.key,P,I))return o(t),i(!1,t.nativeEvent),void(C(f)&&!k&&f.focus());const n=Z.current,u=W(m,e),c=j(m,e);if("Home"===t.key&&(o(t),Z.current=u,_(Z.current)),"End"===t.key&&(o(t),Z.current=c,_(Z.current)),!(S>1&&(Z.current=U(m,{event:t,orientation:P,loop:w,cols:S,disabledIndices:e,minIndex:u,maxIndex:c,prevIndex:Z.current,stopEvent:!0}),_(Z.current),"both"===P))&&yt(t.key,P)){if(o(t),r&&!k&&l(t.currentTarget.ownerDocument)===t.currentTarget)return Z.current=ht(t.key,P,I)?u:c,void _(Z.current);ht(t.key,P,I)?Z.current=w?n>=c?x&&n!==m.current.length?-1:u:z(m,{startingIndex:n,disabledIndices:e}):Math.min(c,z(m,{startingIndex:n,disabledIndices:e})):Z.current=w?n<=u?x&&-1!==n?m.current.length:c:z(m,{startingIndex:n,decrement:!0,disabledIndices:e}):Math.max(u,z(m,{startingIndex:n,decrement:!0,disabledIndices:e})),q(m,Z.current)?_(null):_(Z.current)}}function n(e){"auto"===M&&g(e.nativeEvent)&&(X.current=!0)}const u=k&&r&&ae&&{"aria-activedescendant":ce||ue},c=m.current.find((e=>(null==e?void 0:e.id)===ue));return{reference:{...u,onKeyDown(n){Q.current=!1;const u=0===n.key.indexOf("Arrow"),l=function(e,t,n){return gt(t,n?e===H:e===B,e===K)}(n.key,P,I),s=bt(n.key,P,I),a=yt(n.key,P),f=(R?l:a)||"Enter"===n.key||""===n.key.trim();if(k&&r){const e=null==N?void 0:N.nodesRef.current.find((e=>null==e.parentId)),r=N&&e?function(e,t){let n,r=-1;return function t(o,u){u>r&&(n=o,r=u),Oe(e,o).forEach((e=>{t(e.id,u+1)}))}(t,0),e.find((e=>e.id===n))}(N.nodesRef.current,e.id):null;if(u&&r&&D){const e=new KeyboardEvent("keydown",{key:n.key,bubbles:!0});if(l||s){var d,v;const t=(null==(d=r.context)?void 0:d.elements.domReference)===n.currentTarget,u=s&&!t?null==(v=r.context)?void 0:v.elements.domReference:l?c:null;u&&(o(n),u.dispatchEvent(e),le(void 0))}var p;if(a&&r.context)if(r.context.open&&r.parentId&&n.currentTarget!==r.context.elements.domReference)return o(n),void(null==(p=r.context.elements.domReference)||p.dispatchEvent(e))}return t(n)}(r||O||!u)&&(f&&(G.current=R&&a?null:n.key),R?l&&(o(n),r?(Z.current=W(m,e),_(Z.current)):i(!0,n.nativeEvent)):a&&(null!=E&&(Z.current=E),o(n),!r&&O?i(!0,n.nativeEvent):t(n),r&&_(Z.current)))},onFocus(){r&&_(null)},onPointerDown:function(e){X.current=M,"auto"===M&&y(e.nativeEvent)&&(X.current=!0)},onMouseDown:n,onClick:n},floating:{"aria-orientation":"both"===P?void 0:P,...u,onKeyDown:t,onPointerMove(){Q.current=!0}},item:fe}}),[f,a,ue,ce,ne,re,m,b,P,I,k,r,ae,R,E,O,x,S,w,M,_,i,fe,N,D])}function xt(t,n){void 0===n&&(n={});const{open:r,floatingId:o}=t,{enabled:u=!0,role:i="dialog"}=n,c=ae();return e.useMemo((()=>{const e={id:o,role:i};return u?"tooltip"===i?{reference:{"aria-describedby":r?o:void 0},floating:e}:{reference:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===i?"dialog":i,"aria-controls":r?o:void 0,..."listbox"===i&&{role:"combobox"},..."menu"===i&&{id:c}},floating:{...e,..."menu"===i&&{"aria-labelledby":c}}}:{}}),[u,i,r,o,c])}const wt=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((e,t)=>(t?"-":"")+e.toLowerCase()));function Rt(e,t){return"function"==typeof e?e(t):e}function It(t,n){void 0===n&&(n={});const{open:r,elements:{floating:o}}=t,{duration:u=250}=n,i=("number"==typeof u?u:u.close)||0,[c,l]=e.useState(!1),[s,a]=e.useState("unmounted"),f=function(t,n){const[r,o]=e.useState(t);return t&&!r&&o(!0),e.useEffect((()=>{if(!t){const e=setTimeout((()=>o(!1)),n);return()=>clearTimeout(e)}}),[t,n]),r}(r,i);return V((()=>{c&&!f&&a("unmounted")}),[c,f]),V((()=>{if(o){if(r){a("initial");const e=requestAnimationFrame((()=>{a("open")}));return()=>{cancelAnimationFrame(e)}}l(!0),a("close")}}),[r,o]),{isMounted:f,status:s}}function kt(t,n){void 0===n&&(n={});const{initial:r={opacity:0},open:o,close:u,common:i,duration:c=250}=n,l=t.placement,s=l.split("-")[0],a=e.useMemo((()=>({side:s,placement:l})),[s,l]),f="number"==typeof c,d=(f?c:c.open)||0,m=(f?c:c.close)||0,[v,p]=e.useState((()=>({...Rt(i,a),...Rt(r,a)}))),{isMounted:g,status:y}=It(t,{duration:c}),h=xe(r),b=xe(o),E=xe(u),x=xe(i);return V((()=>{const e=Rt(h.current,a),t=Rt(E.current,a),n=Rt(x.current,a),r=Rt(b.current,a)||Object.keys(e).reduce(((e,t)=>(e[t]="",e)),{});if("initial"===y&&p((t=>({transitionProperty:t.transitionProperty,...n,...e}))),"open"===y&&p({transitionProperty:Object.keys(r).map(wt).join(","),transitionDuration:d+"ms",...n,...r}),"close"===y){const r=t||e;p({transitionProperty:Object.keys(r).map(wt).join(","),transitionDuration:m+"ms",...n,...r})}}),[m,E,h,b,x,d,y,a]),{isMounted:g,styles:v}}function Mt(t,n){var r;const{open:u,dataRef:i}=t,{listRef:c,activeIndex:l,onMatch:s,onTypingChange:a,enabled:f=!0,findMatch:d=null,resetMs:m=750,ignoreKeys:v=[],selectedIndex:p=null}=n,g=e.useRef(),y=e.useRef(""),h=e.useRef(null!=(r=null!=p?p:l)?r:-1),b=e.useRef(null),E=ut(s),x=ut(a),w=xe(d),R=xe(v);return V((()=>{u&&(clearTimeout(g.current),b.current=null,y.current="")}),[u]),V((()=>{var e;u&&""===y.current&&(h.current=null!=(e=null!=p?p:l)?e:-1)}),[u,p,l]),e.useMemo((()=>{if(!f)return{};function e(e){e?i.current.typing||(i.current.typing=e,x(e)):i.current.typing&&(i.current.typing=e,x(e))}function t(e,t,n){const r=w.current?w.current(t,n):t.find((e=>0===(null==e?void 0:e.toLocaleLowerCase().indexOf(n.toLocaleLowerCase()))));return r?e.indexOf(r):-1}function n(n){const r=c.current;if(y.current.length>0&&" "!==y.current[0]&&(-1===t(r,r,y.current)?e(!1):" "===n.key&&o(n)),null==r||R.current.includes(n.key)||1!==n.key.length||n.ctrlKey||n.metaKey||n.altKey)return;u&&" "!==n.key&&(o(n),e(!0));r.every((e=>{var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&y.current===n.key&&(y.current="",h.current=b.current),y.current+=n.key,clearTimeout(g.current),g.current=setTimeout((()=>{y.current="",h.current=b.current,e(!1)}),m);const i=h.current,l=t(r,[...r.slice((i||0)+1),...r.slice(0,(i||0)+1)],y.current);-1!==l?(E(l),b.current=l):" "!==n.key&&(y.current="",e(!1))}return{reference:{onKeyDown:n},floating:{onKeyDown:n,onKeyUp(t){" "===t.key&&e(!1)}}}}),[f,u,i,c,m,R,w,E,x])}function Ct(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}const Tt=e=>({name:"inner",options:e,async fn(t){const{listRef:n,overflowRef:r,onFallbackChange:o,offset:u=0,index:i=0,minItemsVisible:c=4,referenceOverflowThreshold:l=0,scrollRef:s,...a}=e,{rects:f,elements:{floating:d}}=t,m=n.current[i];if(!m)return{};const v={...t,...await I(-m.offsetTop-d.clientTop-f.reference.height/2-m.offsetHeight/2-u).fn(t)},p=(null==s?void 0:s.current)||d,g=await k(Ct(v,p.scrollHeight),a),y=await k(v,{...a,elementContext:"reference"}),h=Math.max(0,g.top),b=v.y+h,E=Math.max(0,p.scrollHeight-h-Math.max(0,g.bottom));return p.style.maxHeight=E+"px",p.scrollTop=h,o&&(p.offsetHeight<m.offsetHeight*Math.min(c,n.current.length-1)-1||y.top>=-l||y.bottom>=-l?D((()=>o(!0))):D((()=>o(!1)))),r&&(r.current=await k(Ct({...v,y:b},p.offsetHeight),a)),{y:b}}});function Ot(t,n){const{open:r,elements:o}=t,{enabled:u=!0,overflowRef:i,scrollRef:c,onChange:l}=n,s=ut(l),a=e.useRef(!1),f=e.useRef(null),d=e.useRef(null);return e.useEffect((()=>{if(!u)return;function e(e){if(e.ctrlKey||!t||null==i.current)return;const n=e.deltaY,r=i.current.top>=-.5,o=i.current.bottom>=-.5,u=t.scrollHeight-t.clientHeight,c=n<0?-1:1,l=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!r&&n>0||!o&&n<0?(e.preventDefault(),D((()=>{s((e=>e+Math[l](n,u*c)))}))):/firefox/i.test(b())&&(t.scrollTop+=n))}const t=(null==c?void 0:c.current)||o.floating;return r&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{f.current=t.scrollTop,null!=i.current&&(d.current={...i.current})})),()=>{f.current=null,d.current=null,t.removeEventListener("wheel",e)}):void 0}),[u,r,o.floating,i,c,s]),e.useMemo((()=>u?{floating:{onKeyDown(){a.current=!0},onWheel(){a.current=!1},onPointerMove(){a.current=!1},onScroll(){const e=(null==c?void 0:c.current)||o.floating;if(i.current&&e&&a.current){if(null!==f.current){const t=e.scrollTop-f.current;(i.current.bottom<-.5&&t<-1||i.current.top<-.5&&t>1)&&D((()=>s((e=>e+t))))}requestAnimationFrame((()=>{f.current=e.scrollTop}))}}}}:{}),[u,i,o.floating,c,s])}function Lt(e,t){const[n,r]=e;let o=!1;const u=t.length;for(let e=0,i=u-1;e<u;i=e++){const[u,c]=t[e]||[0,0],[l,s]=t[i]||[0,0];c>=r!=s>=r&&n<=(l-u)*(r-c)/(s-c)+u&&(o=!o)}return o}function Pt(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let o,u=!1,i=null,l=null,s=performance.now();const a=e=>{let{x:n,y:a,placement:d,elements:m,onClose:v,nodeId:p,tree:g}=e;return function(e){function y(){clearTimeout(o),v()}if(clearTimeout(o),!m.domReference||!m.floating||null==d||null==n||null==a)return;const{clientX:h,clientY:b}=e,E=[h,b],x=f(e),w="mouseleave"===e.type,R=c(m.floating,x),I=c(m.domReference,x),k=m.domReference.getBoundingClientRect(),C=m.floating.getBoundingClientRect(),T=d.split("-")[0],O=n>C.right-C.width/2,L=a>C.bottom-C.height/2,P=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(E,k),S=C.width>k.width,A=C.height>k.height,D=(S?k:C).left,F=(S?k:C).right,N=(A?k:C).top,K=(A?k:C).bottom;if(R&&(u=!0,!w))return;if(I&&(u=!1),I&&!w)return void(u=!0);if(w&&M(e.relatedTarget)&&c(m.floating,e.relatedTarget))return;if(g&&Oe(g.nodesRef.current,p).some((e=>{let{context:t}=e;return null==t?void 0:t.open})))return;if("top"===T&&a>=k.bottom-1||"bottom"===T&&a<=k.top+1||"left"===T&&n>=k.right-1||"right"===T&&n<=k.left+1)return y();let H=[];switch(T){case"top":H=[[D,k.top+1],[D,C.bottom-1],[F,C.bottom-1],[F,k.top+1]];break;case"bottom":H=[[D,C.top+1],[D,k.bottom-1],[F,k.bottom-1],[F,C.top+1]];break;case"left":H=[[C.right-1,K],[C.right-1,N],[k.left+1,N],[k.left+1,K]];break;case"right":H=[[k.right-1,K],[k.right-1,N],[C.left+1,N],[C.left+1,K]]}if(!Lt([h,b],H)){if(u&&!P)return y();if(!w&&r){const t=function(e,t){const n=performance.now(),r=n-s;if(null===i||null===l||0===r)return i=e,l=t,s=n,null;const o=e-i,u=t-l,c=Math.sqrt(o*o+u*u);return i=e,l=t,s=n,c/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return y()}Lt([h,b],function(e){let[n,r]=e;switch(T){case"top":return[[S?n+t/2:O?n+4*t:n-4*t,r+t+1],[S?n-t/2:O?n+4*t:n-4*t,r+t+1],...[[C.left,O||S?C.bottom-t:C.top],[C.right,O?S?C.bottom-t:C.top:C.bottom-t]]];case"bottom":return[[S?n+t/2:O?n+4*t:n-4*t,r-t],[S?n-t/2:O?n+4*t:n-4*t,r-t],...[[C.left,O||S?C.top+t:C.bottom],[C.right,O?S?C.top+t:C.bottom:C.top+t]]];case"left":{const e=[n+t+1,A?r+t/2:L?r+4*t:r-4*t],o=[n+t+1,A?r-t/2:L?r+4*t:r-4*t];return[...[[L||A?C.right-t:C.left,C.top],[L?A?C.right-t:C.left:C.right-t,C.bottom]],e,o]}case"right":return[[n-t,A?r+t/2:L?r+4*t:r-4*t],[n-t,A?r-t/2:L?r+4*t:r-4*t],...[[L||A?C.left+t:C.right,C.top],[L?A?C.left+t:C.right:C.left+t,C.bottom]]]}}([n,a]))?!u&&r&&(o=window.setTimeout(y,40)):y()}}};return a.__options={blockPointerEvents:n},a}export{oe as Composite,ue as CompositeItem,fe as FloatingArrow,Ce as FloatingDelayGroup,Je as FloatingFocusManager,Q as FloatingList,he as FloatingNode,et as FloatingOverlay,Ze as FloatingPortal,be as FloatingTree,Tt as inner,Pt as safePolygon,rt as useClick,ct as useClientPoint,Te as useDelayGroup,Me as useDelayGroupContext,at as useDismiss,ft as useFloating,ye as useFloatingNodeId,pe as useFloatingParentNodeId,Ve as useFloatingPortalNode,ge as useFloatingTree,dt as useFocus,Ie as useHover,ae as useId,Ot as useInnerOffset,vt as useInteractions,J as useListItem,Et as useListNavigation,F as useMergeRefs,xt as useRole,It as useTransitionStatus,kt as useTransitionStyles,Mt as useTypeahead};
