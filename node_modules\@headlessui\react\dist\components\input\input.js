"use client";import{useFocusRing as c}from"@react-aria/focus";import{useHover as F}from"@react-aria/interactions";import{useMemo as g}from"react";import{useId as v}from'../../hooks/use-id.js';import{useDisabled as A}from'../../internal/disabled.js';import{useProvidedId as _}from'../../internal/id.js';import{forwardRefWithAs as D,mergeProps as E,render as R}from'../../utils/render.js';import{useDescribedBy as x}from'../description/description.js';import{useLabelledBy as U}from'../label/label.js';let h="input";function L(o,p){var s;let l=v(),i=_(),u=A(),{id:d=i||`headlessui-input-${l}`,disabled:e=u||!1,invalid:t=!1,...f}=o,m=U(),T=x(),{isFocusVisible:r,focusProps:y}=c({isTextInput:!0,autoFocus:(s=o.autoFocus)!=null?s:!1}),{isHovered:n,hoverProps:b}=F({isDisabled:e!=null?e:!1}),I=E({ref:p,id:d,"aria-labelledby":m,"aria-describedby":T,"aria-invalid":t?"":void 0,disabled:e||void 0},y,b),P=g(()=>{var a;return{disabled:e,invalid:t,hover:n,focus:r,autofocus:(a=o.autoFocus)!=null?a:!1}},[e,t,n,r,o.autoFocus]);return R({ourProps:I,theirProps:f,slot:P,defaultTag:h,name:"Input"})}let J=D(L);export{J as Input};
