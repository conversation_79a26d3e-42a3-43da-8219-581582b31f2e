import o,{useCallback as y,useMemo as x,useRef as F}from"react";import{FocusTrap as I,FocusTrapFeatures as P}from'../components/focus-trap/focus-trap.js';import{Portal as k,useNestedPortals as W}from'../components/portal/portal.js';import{useDocumentOverflowLockedEffect as h}from'../hooks/document-overflow/use-document-overflow.js';import{useId as j}from'../hooks/use-id.js';import{useInert as g}from'../hooks/use-inert.js';import{useOwnerDocument as U}from'../hooks/use-owner.js';import{useRootContainers as B}from'../hooks/use-root-containers.js';import{useSyncRefs as G}from'../hooks/use-sync-refs.js';import{HoistFormFields as q}from'../internal/form-fields.js';import{RenderFeatures as A,forwardRefWithAs as w,render as $}from'../utils/render.js';import{ForcePortalRoot as R}from'./portal-force-root.js';function J(f,s,i=()=>[document.body]){h(f,s,d=>{var p;return{containers:[...(p=d.containers)!=null?p:[],i]}})}var X=(e=>(e[e.None=0]="None",e[e.Inert=1]="Inert",e[e.ScrollLock=2]="ScrollLock",e[e.FocusTrap=4]="FocusTrap",e[e.All=7]="All",e))(X||{});let z="div",K=A.RenderStrategy|A.Static;function Q(f,s){let i=j(),{id:d=`headlessui-modal-${i}`,initialFocus:p,role:e="dialog",features:l=7,enabled:c=!0,focusTrapFeatures:L=P.All,...E}=f;c||(l=0);let T=F(!1);e=function(){return e==="dialog"||e==="alertdialog"?e:(T.current||(T.current=!0,console.warn(`Invalid role [${e}] passed to <Modal />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let m=F(null),O=G(m,s),r=U(m),[b,H]=W(),D={get current(){return m.current}},{resolveContainers:M,mainTreeNodeRef:u,MainTreeNode:_}=B({portals:b,defaultContainers:[D]}),C=y(()=>{var a,n;return(n=Array.from((a=r==null?void 0:r.querySelectorAll("body > *"))!=null?a:[]).find(t=>t.id==="headlessui-portal-root"?!1:t.contains(u.current)&&t instanceof HTMLElement))!=null?n:null},[u]);g(C,!!(l&1));let v=y(()=>{var a,n;return(n=Array.from((a=r==null?void 0:r.querySelectorAll("[data-headlessui-portal]"))!=null?a:[]).find(t=>t.contains(u.current)&&t instanceof HTMLElement))!=null?n:null},[u]);g(v,!!(l&1)),J(r,!!(l&2),M);let N=x(()=>({}),[]),S={ref:O,id:d,role:e,"aria-modal":c||void 0};return o.createElement(o.Fragment,null,o.createElement(R,{force:!0},o.createElement(k,null,o.createElement(I,{initialFocus:p,containers:M,features:l&4?L:P.None},o.createElement(R,{force:!1},o.createElement(H,null,$({ourProps:S,theirProps:E,slot:N,defaultTag:z,features:K,name:"Modal"})))))),o.createElement(q,null,o.createElement(_,null)))}let V=w(Q),pe=Object.assign(V,{});export{pe as Modal,X as ModalFeatures};
