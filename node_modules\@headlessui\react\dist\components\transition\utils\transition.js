import{disposables as s}from'../../../utils/disposables.js';import{match as g}from'../../../utils/match.js';import{once as b}from'../../../utils/once.js';function v(t,...e){t&&e.length>0&&t.classList.add(...e)}function f(t,...e){t&&e.length>0&&t.classList.remove(...e)}function F(t,e){let i=s();if(!t)return i.dispose;let{transitionDuration:u,transitionDelay:a}=getComputedStyle(t),[m,d]=[u,a].map(l=>{let[r=0]=l.split(",").filter(Boolean).map(o=>o.includes("ms")?parseFloat(o):parseFloat(o)*1e3).sort((o,p)=>p-o);return r}),n=m+d;if(n!==0){let l=i.group(r=>{let o=r.setTimeout(()=>{e(),r.dispose()},n);r.addEventListener(t,"transitionrun",p=>{p.target===p.currentTarget&&(o(),r.addEventListener(t,"transitioncancel",T=>{T.target===T.currentTarget&&(e(),l())}))})});i.addEventListener(t,"transitionend",r=>{r.target===r.currentTarget&&(e(),i.dispose())})}else e();return i.add(()=>e()),i.dispose}function y(t,e,i,u){let a=i?"enter":"leave",m=s(),d=u!==void 0?b(u):()=>{};a==="enter"&&(t.removeAttribute("hidden"),t.style.display="");let n=g(a,{enter:()=>e.enter,leave:()=>e.leave}),l=g(a,{enter:()=>e.enterTo,leave:()=>e.leaveTo}),r=g(a,{enter:()=>e.enterFrom,leave:()=>e.leaveFrom});return f(t,...e.base,...e.enter,...e.enterTo,...e.enterFrom,...e.leave,...e.leaveFrom,...e.leaveTo,...e.entered),v(t,...e.base,...n,...r),m.nextFrame(()=>{f(t,...e.base,...n,...r),v(t,...e.base,...n,...l),F(t,()=>(f(t,...e.base,...n),v(t,...e.base,...e.entered),d()))}),m.dispose}export{y as transition};
