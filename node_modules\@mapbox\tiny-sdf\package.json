{"name": "@mapbox/tiny-sdf", "version": "1.2.5", "description": "Browser-side SDF font generator", "main": "index.js", "scripts": {"test": "eslint index.js"}, "repository": {"type": "git", "url": "git+https://github.com/mapbox/tiny-sdf.git"}, "keywords": ["sdf", "signed distance fields", "font", "canvas", "text", "distance transform"], "author": "<PERSON>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/mapbox/tiny-sdf/issues"}, "homepage": "https://github.com/mapbox/tiny-sdf#readme", "devDependencies": {"eslint": "^7.0.0", "eslint-config-mourner": "^2.0.1"}, "eslintConfig": {"extends": "mourner"}}