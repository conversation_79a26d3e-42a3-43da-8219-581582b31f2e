"use client";import{useFocusRing as v}from"@react-aria/focus";import{useHover as A}from"@react-aria/interactions";import{useMemo as F}from"react";import{useActivePress as g}from'../../hooks/use-active-press.js';import{useId as _}from'../../hooks/use-id.js';import{useDisabled as D}from'../../internal/disabled.js';import{useProvidedId as L}from'../../internal/id.js';import{forwardRefWithAs as R,mergeProps as C,render as x}from'../../utils/render.js';import{useDescribedBy as h}from'../description/description.js';import{useLabelledBy as H}from'../label/label.js';let B="select";function G(o,n){var a;let p=_(),d=L(),c=D(),{id:f=d||`headlessui-select-${p}`,disabled:e=c||!1,invalid:t=!1,...u}=o,m=H(),T=h(),{isFocusVisible:r,focusProps:y}=v({autoFocus:(a=o.autoFocus)!=null?a:!1}),{isHovered:l,hoverProps:b}=A({isDisabled:e!=null?e:!1}),{pressed:s,pressProps:P}=g({disabled:e!=null?e:!1}),S=C({ref:n,id:f,"aria-labelledby":m,"aria-describedby":T,"aria-invalid":t?"":void 0,disabled:e||void 0},y,b,P),E=F(()=>{var i;return{disabled:e,invalid:t,hover:l,focus:r,active:s,autofocus:(i=o.autoFocus)!=null?i:!1}},[e,t,l,r,s,o.autoFocus]);return x({ourProps:S,theirProps:u,slot:E,defaultTag:B,name:"Select"})}let $=R(G);export{$ as Select};
