"use client";import r,{useMemo as F}from"react";import{useId as T}from'../../hooks/use-id.js';import{Disabled<PERSON>rovider as P,useDisabled as f}from'../../internal/disabled.js';import{FormFieldsProvider as y}from'../../internal/form-fields.js';import{IdProvider as u}from'../../internal/id.js';import{forwardRefWithAs as D,render as v}from'../../utils/render.js';import{useDescriptions as b}from'../description/description.js';import{useLabels as E}from'../label/label.js';let A="div";function L(i,d){let l=`headlessui-control-${T()}`,[t,s]=E(),[p,n]=b(),a=f(),{disabled:e=a||!1,...o}=i,m=F(()=>({disabled:e}),[e]);return r.createElement(P,{value:e},r.createElement(s,{value:t},r.createElement(n,{value:p},r.createElement(u,{id:l},v({ourProps:{ref:d,disabled:e,"aria-disabled":e||void 0},theirProps:{...o,children:r.createElement(y,null,o.children)},slot:m,defaultTag:A,name:"Field"})))))}let H=D(L);export{H as Field};
