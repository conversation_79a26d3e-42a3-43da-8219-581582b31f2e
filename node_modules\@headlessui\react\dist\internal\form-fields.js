import o,{createContext as a,useContext as H,useEffect as m,useState as s}from"react";import{createPortal as v}from"react-dom";import{useDisposables as E}from'../hooks/use-disposables.js';import{objectToFormEntries as g}from'../utils/form.js';import{compact as h}from'../utils/render.js';import{Hidden as i,HiddenFeatures as l}from'./hidden.js';let u=a(null);function b(t){let[r,e]=s(null);return o.createElement(u.Provider,{value:{target:r}},t.children,o.createElement(i,{features:l.Hidden,ref:e}))}function x({children:t}){let r=H(u);if(!r)return o.createElement(o.Fragment,null,t);let{target:e}=r;return e?v(o.createElement(o.Fragment,null,t),e):null}function O({data:t,form:r,onReset:e}){let[n,f]=s(null),F=E();return m(()=>{if(e&&n)return F.addEventListener(n,"reset",e)},[n,r,e]),o.createElement(x,null,o.createElement(y,{setForm:f,formId:r}),g(t).map(([d,p])=>o.createElement(i,{features:l.Hidden,...h({key:d,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:r,name:d,value:p})})))}function y({setForm:t,formId:r}){return m(()=>{if(r){let e=document.getElementById(r);e&&t(e)}},[t,r]),r?null:o.createElement(i,{features:l.Hidden,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:e=>{if(!e)return;let n=e.closest("form");n&&t(n)}})}export{O as FormFields,b as FormFieldsProvider,x as HoistFormFields};
