import N,{createContext as Z,createRef as xe,Fragment as ye,use<PERSON><PERSON>back as ge,useContext as ee,useEffect as te,useMemo as E,useReducer as Le,useRef as h}from"react";import{useComputed as oe}from'../../hooks/use-computed.js';import{useControllable as Oe}from'../../hooks/use-controllable.js';import{useDisposables as j}from'../../hooks/use-disposables.js';import{useEvent as f}from'../../hooks/use-event.js';import{useId as V}from'../../hooks/use-id.js';import{useIsoMorphicEffect as K}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as me}from'../../hooks/use-latest-value.js';import{useOutsideClick as Re}from'../../hooks/use-outside-click.js';import{useResolveButtonType as ve}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as _}from'../../hooks/use-sync-refs.js';import{useTextValue as Ae}from'../../hooks/use-text-value.js';import{useTrackedPointer as Se}from'../../hooks/use-tracked-pointer.js';import{Features as Pe,Hidden as Ee}from'../../internal/hidden.js';import{OpenClosedProvider as he,State as Q,useOpenClosed as De}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as Ie}from'../../utils/bugs.js';import{calculateActiveIndex as Ce,Focus as v}from'../../utils/calculate-active-index.js';import{disposables as $}from'../../utils/disposables.js';import{FocusableMode as _e,isFocusableElement as Fe,sortByDomNode as Me}from'../../utils/focus-management.js';import{objectToFormEntries as ke}from'../../utils/form.js';import{match as D}from'../../utils/match.js';import{getOwnerDocument as we}from'../../utils/owner.js';import{compact as Ue,Features as ne,forwardRefWithAs as F,render as M}from'../../utils/render.js';import{Keys as y}from'../keyboard.js';var Be=(n=>(n[n.Open=0]="Open",n[n.Closed=1]="Closed",n))(Be||{}),He=(n=>(n[n.Single=0]="Single",n[n.Multi=1]="Multi",n))(He||{}),Ge=(n=>(n[n.Pointer=0]="Pointer",n[n.Other=1]="Other",n))(Ge||{}),Ne=(i=>(i[i.OpenListbox=0]="OpenListbox",i[i.CloseListbox=1]="CloseListbox",i[i.GoToOption=2]="GoToOption",i[i.Search=3]="Search",i[i.ClearSearch=4]="ClearSearch",i[i.RegisterOption=5]="RegisterOption",i[i.UnregisterOption=6]="UnregisterOption",i[i.RegisterLabel=7]="RegisterLabel",i))(Ne||{});function z(e,a=n=>n){let n=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,r=Me(a(e.options.slice()),t=>t.dataRef.current.domRef.current),l=n?r.indexOf(n):null;return l===-1&&(l=null),{options:r,activeOptionIndex:l}}let je={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,listboxState:1}},[0](e){if(e.dataRef.current.disabled||e.listboxState===0)return e;let a=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,r=e.options.findIndex(l=>n(l.dataRef.current.value));return r!==-1&&(a=r),{...e,listboxState:0,activeOptionIndex:a}},[2](e,a){var l;if(e.dataRef.current.disabled||e.listboxState===1)return e;let n=z(e),r=Ce(a,{resolveItems:()=>n.options,resolveActiveIndex:()=>n.activeOptionIndex,resolveId:t=>t.id,resolveDisabled:t=>t.dataRef.current.disabled});return{...e,...n,searchQuery:"",activeOptionIndex:r,activationTrigger:(l=a.trigger)!=null?l:1}},[3]:(e,a)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let r=e.searchQuery!==""?0:1,l=e.searchQuery+a.value.toLowerCase(),p=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+r).concat(e.options.slice(0,e.activeOptionIndex+r)):e.options).find(i=>{var b;return!i.dataRef.current.disabled&&((b=i.dataRef.current.textValue)==null?void 0:b.startsWith(l))}),u=p?e.options.indexOf(p):-1;return u===-1||u===e.activeOptionIndex?{...e,searchQuery:l}:{...e,searchQuery:l,activeOptionIndex:u,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,a)=>{let n={id:a.id,dataRef:a.dataRef},r=z(e,l=>[...l,n]);return e.activeOptionIndex===null&&e.dataRef.current.isSelected(a.dataRef.current.value)&&(r.activeOptionIndex=r.options.indexOf(n)),{...e,...r}},[6]:(e,a)=>{let n=z(e,r=>{let l=r.findIndex(t=>t.id===a.id);return l!==-1&&r.splice(l,1),r});return{...e,...n,activationTrigger:1}},[7]:(e,a)=>({...e,labelId:a.id})},J=Z(null);J.displayName="ListboxActionsContext";function k(e){let a=ee(J);if(a===null){let n=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,k),n}return a}let q=Z(null);q.displayName="ListboxDataContext";function w(e){let a=ee(q);if(a===null){let n=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,w),n}return a}function Ve(e,a){return D(a.type,je,e,a)}let Ke=ye;function Qe(e,a){let{value:n,defaultValue:r,form:l,name:t,onChange:p,by:u=(s,c)=>s===c,disabled:i=!1,horizontal:b=!1,multiple:R=!1,...m}=e;const P=b?"horizontal":"vertical";let S=_(a),[g=R?[]:void 0,x]=Oe(n,p,r),[T,o]=Le(Ve,{dataRef:xe(),listboxState:1,options:[],searchQuery:"",labelId:null,activeOptionIndex:null,activationTrigger:1}),L=h({static:!1,hold:!1}),U=h(null),B=h(null),W=h(null),I=f(typeof u=="string"?(s,c)=>{let O=u;return(s==null?void 0:s[O])===(c==null?void 0:c[O])}:u),A=ge(s=>D(d.mode,{[1]:()=>g.some(c=>I(c,s)),[0]:()=>I(g,s)}),[g]),d=E(()=>({...T,value:g,disabled:i,mode:R?1:0,orientation:P,compare:I,isSelected:A,optionsPropsRef:L,labelRef:U,buttonRef:B,optionsRef:W}),[g,i,R,T]);K(()=>{T.dataRef.current=d},[d]),Re([d.buttonRef,d.optionsRef],(s,c)=>{var O;o({type:1}),Fe(c,_e.Loose)||(s.preventDefault(),(O=d.buttonRef.current)==null||O.focus())},d.listboxState===0);let H=E(()=>({open:d.listboxState===0,disabled:i,value:g}),[d,i,g]),ie=f(s=>{let c=d.options.find(O=>O.id===s);c&&X(c.dataRef.current.value)}),re=f(()=>{if(d.activeOptionIndex!==null){let{dataRef:s,id:c}=d.options[d.activeOptionIndex];X(s.current.value),o({type:2,focus:v.Specific,id:c})}}),ae=f(()=>o({type:0})),le=f(()=>o({type:1})),se=f((s,c,O)=>s===v.Specific?o({type:2,focus:v.Specific,id:c,trigger:O}):o({type:2,focus:s,trigger:O})),pe=f((s,c)=>(o({type:5,id:s,dataRef:c}),()=>o({type:6,id:s}))),ue=f(s=>(o({type:7,id:s}),()=>o({type:7,id:null}))),X=f(s=>D(d.mode,{[0](){return x==null?void 0:x(s)},[1](){let c=d.value.slice(),O=c.findIndex(C=>I(C,s));return O===-1?c.push(s):c.splice(O,1),x==null?void 0:x(c)}})),de=f(s=>o({type:3,value:s})),ce=f(()=>o({type:4})),fe=E(()=>({onChange:X,registerOption:pe,registerLabel:ue,goToOption:se,closeListbox:le,openListbox:ae,selectActiveOption:re,selectOption:ie,search:de,clearSearch:ce}),[]),Te={ref:S},G=h(null),be=j();return te(()=>{G.current&&r!==void 0&&be.addEventListener(G.current,"reset",()=>{x==null||x(r)})},[G,x]),N.createElement(J.Provider,{value:fe},N.createElement(q.Provider,{value:d},N.createElement(he,{value:D(d.listboxState,{[0]:Q.Open,[1]:Q.Closed})},t!=null&&g!=null&&ke({[t]:g}).map(([s,c],O)=>N.createElement(Ee,{features:Pe.Hidden,ref:O===0?C=>{var Y;G.current=(Y=C==null?void 0:C.closest("form"))!=null?Y:null}:void 0,...Ue({key:s,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:l,name:s,value:c})})),M({ourProps:Te,theirProps:m,slot:H,defaultTag:Ke,name:"Listbox"}))))}let We="button";function Xe(e,a){var x;let n=V(),{id:r=`headlessui-listbox-button-${n}`,...l}=e,t=w("Listbox.Button"),p=k("Listbox.Button"),u=_(t.buttonRef,a),i=j(),b=f(T=>{switch(T.key){case y.Space:case y.Enter:case y.ArrowDown:T.preventDefault(),p.openListbox(),i.nextFrame(()=>{t.value||p.goToOption(v.First)});break;case y.ArrowUp:T.preventDefault(),p.openListbox(),i.nextFrame(()=>{t.value||p.goToOption(v.Last)});break}}),R=f(T=>{switch(T.key){case y.Space:T.preventDefault();break}}),m=f(T=>{if(Ie(T.currentTarget))return T.preventDefault();t.listboxState===0?(p.closeListbox(),i.nextFrame(()=>{var o;return(o=t.buttonRef.current)==null?void 0:o.focus({preventScroll:!0})})):(T.preventDefault(),p.openListbox())}),P=oe(()=>{if(t.labelId)return[t.labelId,r].join(" ")},[t.labelId,r]),S=E(()=>({open:t.listboxState===0,disabled:t.disabled,value:t.value}),[t]),g={ref:u,id:r,type:ve(e,t.buttonRef),"aria-haspopup":"listbox","aria-controls":(x=t.optionsRef.current)==null?void 0:x.id,"aria-expanded":t.listboxState===0,"aria-labelledby":P,disabled:t.disabled,onKeyDown:b,onKeyUp:R,onClick:m};return M({ourProps:g,theirProps:l,slot:S,defaultTag:We,name:"Listbox.Button"})}let $e="label";function ze(e,a){let n=V(),{id:r=`headlessui-listbox-label-${n}`,...l}=e,t=w("Listbox.Label"),p=k("Listbox.Label"),u=_(t.labelRef,a);K(()=>p.registerLabel(r),[r]);let i=f(()=>{var m;return(m=t.buttonRef.current)==null?void 0:m.focus({preventScroll:!0})}),b=E(()=>({open:t.listboxState===0,disabled:t.disabled}),[t]);return M({ourProps:{ref:u,id:r,onClick:i},theirProps:l,slot:b,defaultTag:$e,name:"Listbox.Label"})}let Je="ul",qe=ne.RenderStrategy|ne.Static;function Ye(e,a){var T;let n=V(),{id:r=`headlessui-listbox-options-${n}`,...l}=e,t=w("Listbox.Options"),p=k("Listbox.Options"),u=_(t.optionsRef,a),i=j(),b=j(),R=De(),m=(()=>R!==null?(R&Q.Open)===Q.Open:t.listboxState===0)();te(()=>{var L;let o=t.optionsRef.current;o&&t.listboxState===0&&o!==((L=we(o))==null?void 0:L.activeElement)&&o.focus({preventScroll:!0})},[t.listboxState,t.optionsRef]);let P=f(o=>{switch(b.dispose(),o.key){case y.Space:if(t.searchQuery!=="")return o.preventDefault(),o.stopPropagation(),p.search(o.key);case y.Enter:if(o.preventDefault(),o.stopPropagation(),t.activeOptionIndex!==null){let{dataRef:L}=t.options[t.activeOptionIndex];p.onChange(L.current.value)}t.mode===0&&(p.closeListbox(),$().nextFrame(()=>{var L;return(L=t.buttonRef.current)==null?void 0:L.focus({preventScroll:!0})}));break;case D(t.orientation,{vertical:y.ArrowDown,horizontal:y.ArrowRight}):return o.preventDefault(),o.stopPropagation(),p.goToOption(v.Next);case D(t.orientation,{vertical:y.ArrowUp,horizontal:y.ArrowLeft}):return o.preventDefault(),o.stopPropagation(),p.goToOption(v.Previous);case y.Home:case y.PageUp:return o.preventDefault(),o.stopPropagation(),p.goToOption(v.First);case y.End:case y.PageDown:return o.preventDefault(),o.stopPropagation(),p.goToOption(v.Last);case y.Escape:return o.preventDefault(),o.stopPropagation(),p.closeListbox(),i.nextFrame(()=>{var L;return(L=t.buttonRef.current)==null?void 0:L.focus({preventScroll:!0})});case y.Tab:o.preventDefault(),o.stopPropagation();break;default:o.key.length===1&&(p.search(o.key),b.setTimeout(()=>p.clearSearch(),350));break}}),S=oe(()=>{var o;return(o=t.buttonRef.current)==null?void 0:o.id},[t.buttonRef.current]),g=E(()=>({open:t.listboxState===0}),[t]),x={"aria-activedescendant":t.activeOptionIndex===null||(T=t.options[t.activeOptionIndex])==null?void 0:T.id,"aria-multiselectable":t.mode===1?!0:void 0,"aria-labelledby":S,"aria-orientation":t.orientation,id:r,onKeyDown:P,role:"listbox",tabIndex:0,ref:u};return M({ourProps:x,theirProps:l,slot:g,defaultTag:Je,features:qe,visible:m,name:"Listbox.Options"})}let Ze="li";function et(e,a){let n=V(),{id:r=`headlessui-listbox-option-${n}`,disabled:l=!1,value:t,...p}=e,u=w("Listbox.Option"),i=k("Listbox.Option"),b=u.activeOptionIndex!==null?u.options[u.activeOptionIndex].id===r:!1,R=u.isSelected(t),m=h(null),P=Ae(m),S=me({disabled:l,value:t,domRef:m,get textValue(){return P()}}),g=_(a,m);K(()=>{if(u.listboxState!==0||!b||u.activationTrigger===0)return;let A=$();return A.requestAnimationFrame(()=>{var d,H;(H=(d=m.current)==null?void 0:d.scrollIntoView)==null||H.call(d,{block:"nearest"})}),A.dispose},[m,b,u.listboxState,u.activationTrigger,u.activeOptionIndex]),K(()=>i.registerOption(r,S),[S,r]);let x=f(A=>{if(l)return A.preventDefault();i.onChange(t),u.mode===0&&(i.closeListbox(),$().nextFrame(()=>{var d;return(d=u.buttonRef.current)==null?void 0:d.focus({preventScroll:!0})}))}),T=f(()=>{if(l)return i.goToOption(v.Nothing);i.goToOption(v.Specific,r)}),o=Se(),L=f(A=>o.update(A)),U=f(A=>{o.wasMoved(A)&&(l||b||i.goToOption(v.Specific,r,0))}),B=f(A=>{o.wasMoved(A)&&(l||b&&i.goToOption(v.Nothing))}),W=E(()=>({active:b,selected:R,disabled:l}),[b,R,l]);return M({ourProps:{id:r,ref:g,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":R,disabled:void 0,onClick:x,onFocus:T,onPointerEnter:L,onMouseEnter:L,onPointerMove:U,onMouseMove:U,onPointerLeave:B,onMouseLeave:B},theirProps:p,slot:W,defaultTag:Ze,name:"Listbox.Option"})}let tt=F(Qe),ot=F(Xe),nt=F(ze),it=F(Ye),rt=F(et),It=Object.assign(tt,{Button:ot,Label:nt,Options:it,Option:rt});export{It as Listbox};
