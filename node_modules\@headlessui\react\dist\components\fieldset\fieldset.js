"use client";import t,{use<PERSON>emo as a}from"react";import{DisabledProvider as n,useDisabled as T}from'../../internal/disabled.js';import{forwardRefWithAs as m,render as F}from'../../utils/render.js';import{useLabels as y}from'../label/label.js';let f="div";function E(r,o){let l=T(),{disabled:e=l||!1,...s}=r,[i,d]=y(),p=a(()=>({disabled:e}),[e]);return t.createElement(n,{value:e},t.createElement(d,null,F({ourProps:{ref:o,role:"group","aria-labelledby":i,"aria-disabled":e||void 0},theirProps:s,slot:p,defaultTag:f,name:"Fieldset"})))}let L=m(E);export{L as Fieldset};
