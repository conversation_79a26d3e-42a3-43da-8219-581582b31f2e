"use client";import m,{Fragment as Z,createContext as $,useContext as W,useEffect as pe,useMemo as ee,useRef as c,useState as J}from"react";import{useDisposables as Ce}from'../../hooks/use-disposables.js';import{useEvent as E}from'../../hooks/use-event.js';import{useFlags as he}from'../../hooks/use-flags.js';import{useIsMounted as ve}from'../../hooks/use-is-mounted.js';import{useIsoMorphicEffect as H}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as V}from'../../hooks/use-latest-value.js';import{useServerHandoffComplete as te}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as ne}from'../../hooks/use-sync-refs.js';import{useTransition as ge}from'../../hooks/use-transition.js';import{OpenClosedProvider as Ee,State as b,useOpenClosed as re}from'../../internal/open-closed.js';import{classNames as ie}from'../../utils/class-names.js';import{match as F}from'../../utils/match.js';import{RenderFeatures as be,RenderStrategy as y,forwardRefWithAs as X,render as oe}from'../../utils/render.js';function S(t=""){return t.split(/\s+/).filter(n=>n.length>1)}let w=$(null);w.displayName="TransitionContext";var Se=(r=>(r.Visible="visible",r.Hidden="hidden",r))(Se||{});function ye(){let t=W(w);if(t===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return t}function xe(){let t=W(M);if(t===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return t}let M=$(null);M.displayName="NestingContext";function U(t){return"children"in t?U(t.children):t.current.filter(({el:n})=>n.current!==null).filter(({state:n})=>n==="visible").length>0}function se(t,n){let r=V(t),s=c([]),R=ve(),D=Ce(),p=E((i,e=y.Hidden)=>{let a=s.current.findIndex(({el:o})=>o===i);a!==-1&&(F(e,{[y.Unmount](){s.current.splice(a,1)},[y.Hidden](){s.current[a].state="hidden"}}),D.microTask(()=>{var o;!U(s)&&R.current&&((o=r.current)==null||o.call(r))}))}),x=E(i=>{let e=s.current.find(({el:a})=>a===i);return e?e.state!=="visible"&&(e.state="visible"):s.current.push({el:i,state:"visible"}),()=>p(i,y.Unmount)}),C=c([]),h=c(Promise.resolve()),u=c({enter:[],leave:[],idle:[]}),v=E((i,e,a)=>{C.current.splice(0),n&&(n.chains.current[e]=n.chains.current[e].filter(([o])=>o!==i)),n==null||n.chains.current[e].push([i,new Promise(o=>{C.current.push(o)})]),n==null||n.chains.current[e].push([i,new Promise(o=>{Promise.all(u.current[e].map(([f,N])=>N)).then(()=>o())})]),e==="enter"?h.current=h.current.then(()=>n==null?void 0:n.wait.current).then(()=>a(e)):a(e)}),d=E((i,e,a)=>{Promise.all(u.current[e].splice(0).map(([o,f])=>f)).then(()=>{var o;(o=C.current.shift())==null||o()}).then(()=>a(e))});return ee(()=>({children:s,register:x,unregister:p,onStart:v,onStop:d,wait:h,chains:u}),[x,p,s,v,d,u,h])}function Ne(){}let Pe=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function ae(t){var r;let n={};for(let s of Pe)n[s]=(r=t[s])!=null?r:Ne;return n}function Re(t){let n=c(ae(t));return pe(()=>{n.current=ae(t)},[t]),n}let De="div",le=be.RenderStrategy;function _e(t,n){var Q,Y;let{beforeEnter:r,afterEnter:s,beforeLeave:R,afterLeave:D,enter:p,enterFrom:x,enterTo:C,entered:h,leave:u,leaveFrom:v,leaveTo:d,...i}=t,e=c(null),a=ne(e,n),o=(Q=i.unmount)==null||Q?y.Unmount:y.Hidden,{show:f,appear:N,initial:T}=ye(),[l,j]=J(f?"visible":"hidden"),z=xe(),{register:L,unregister:A}=z;H(()=>L(e),[L,e]),H(()=>{if(o===y.Hidden&&e.current){if(f&&l!=="visible"){j("visible");return}return F(l,{["hidden"]:()=>A(e),["visible"]:()=>L(e)})}},[l,e,L,A,f,o]);let k=V({base:S(i.className),enter:S(p),enterFrom:S(x),enterTo:S(C),entered:S(h),leave:S(u),leaveFrom:S(v),leaveTo:S(d)}),O=Re({beforeEnter:r,afterEnter:s,beforeLeave:R,afterLeave:D}),G=te();H(()=>{if(G&&l==="visible"&&e.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[e,l,G]);let Te=T&&!N,K=N&&f&&T,de=(()=>!G||Te?"idle":f?"enter":"leave")(),_=he(0),fe=E(g=>F(g,{enter:()=>{_.addFlag(b.Opening),O.current.beforeEnter()},leave:()=>{_.addFlag(b.Closing),O.current.beforeLeave()},idle:()=>{}})),me=E(g=>F(g,{enter:()=>{_.removeFlag(b.Opening),O.current.afterEnter()},leave:()=>{_.removeFlag(b.Closing),O.current.afterLeave()},idle:()=>{}})),I=se(()=>{j("hidden"),A(e)},z),B=c(!1);ge({immediate:K,container:e,classes:k,direction:de,onStart:V(g=>{B.current=!0,I.onStart(e,g,fe)}),onStop:V(g=>{B.current=!1,I.onStop(e,g,me),g==="leave"&&!U(I)&&(j("hidden"),A(e))})});let P=i,ce={ref:a};return K?P={...P,className:ie(i.className,...k.current.enter,...k.current.enterFrom)}:B.current&&(P.className=ie(i.className,(Y=e.current)==null?void 0:Y.className),P.className===""&&delete P.className),m.createElement(M.Provider,{value:I},m.createElement(Ee,{value:F(l,{["visible"]:b.Open,["hidden"]:b.Closed})|_.flags},oe({ourProps:ce,theirProps:P,defaultTag:De,features:le,visible:l==="visible",name:"Transition.Child"})))}function He(t,n){let{show:r,appear:s=!1,unmount:R=!0,...D}=t,p=c(null),x=ne(p,n);te();let C=re();if(r===void 0&&C!==null&&(r=(C&b.Open)===b.Open),![!0,!1].includes(r))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[h,u]=J(r?"visible":"hidden"),v=se(()=>{u("hidden")}),[d,i]=J(!0),e=c([r]);H(()=>{d!==!1&&e.current[e.current.length-1]!==r&&(e.current.push(r),i(!1))},[e,r]);let a=ee(()=>({show:r,appear:s,initial:d}),[r,s,d]);H(()=>{if(r)u("visible");else if(!U(v))u("hidden");else{let T=p.current;if(!T)return;let l=T.getBoundingClientRect();l.x===0&&l.y===0&&l.width===0&&l.height===0&&u("hidden")}},[r,v]);let o={unmount:R},f=E(()=>{var T;d&&i(!1),(T=t.beforeEnter)==null||T.call(t)}),N=E(()=>{var T;d&&i(!1),(T=t.beforeLeave)==null||T.call(t)});return m.createElement(M.Provider,{value:v},m.createElement(w.Provider,{value:a},oe({ourProps:{...o,as:Z,children:m.createElement(ue,{ref:x,...o,...D,beforeEnter:f,beforeLeave:N})},theirProps:{},defaultTag:Z,features:le,visible:h==="visible",name:"Transition"})))}function Fe(t,n){let r=W(w)!==null,s=re()!==null;return m.createElement(m.Fragment,null,!r&&s?m.createElement(q,{ref:n,...t}):m.createElement(ue,{ref:n,...t}))}let q=X(He),ue=X(_e),Le=X(Fe),qe=Object.assign(q,{Child:Le,Root:q});export{qe as Transition,Le as TransitionChild};
