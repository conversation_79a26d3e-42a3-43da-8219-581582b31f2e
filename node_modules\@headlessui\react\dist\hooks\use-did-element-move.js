import{useRef as o}from"react";import{useIsoMorphicEffect as f}from'./use-iso-morphic-effect.js';function p(e,r=!0){let t=o({left:0,top:0});if(f(()=>{let u=e.current;if(!u)return;let l=u.getBoundingClientRect();l&&(t.current=l)},[r]),e.current==null||!r||e.current===document.activeElement)return!1;let n=e.current.getBoundingClientRect();return n.top!==t.current.top||n.left!==t.current.left}export{p as useDidElementMove};
