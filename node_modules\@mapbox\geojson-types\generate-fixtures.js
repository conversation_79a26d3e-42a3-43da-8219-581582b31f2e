const fs = require('fs');
const fixtures = require('@mapbox/geojson-fixtures');

for (let key in fixtures.geometry) {
    fs.writeFileSync(`${__dirname}/fixtures/valid/${key}.js.flow`, `// @flow
// This file was generated by generate-fixtures.js. Do not edit!
import type {
    GeoJSONGeometry
} from '../../index';

const geojson: GeoJSONGeometry = ${JSON.stringify(fixtures.geometry[key], null, 2)}
`);
}
