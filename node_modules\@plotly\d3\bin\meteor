#!/usr/bin/env node

var fs = require("fs"),
    package = require("../package.json");

console.log(
  "// Package metadata for Meteor.js.\n" +
  "\n" +
  "Package.describe({\n" +
  "  name: \"d3js:d3\", // http://atmospherejs.com/d3js/d3\n" +
  "  summary: \"D3 (official): " + package.description + "\",\n" +
  "  version: \"" + package.version + "\",\n" +
  "  git: \"https://github.com/mbostock/d3.git\"\n" +
  "});\n" +
  "\n" +
  "Package.onUse(function(api) {\n" +
  "  api.versionsFrom([\"METEOR@1.0\"]);\n" +
  "  api.addFiles(\"d3.js\", \"client\");\n" +
  "});"
);
