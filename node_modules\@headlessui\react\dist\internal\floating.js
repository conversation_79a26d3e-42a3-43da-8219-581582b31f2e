import{autoUpdate as Z,flip as ee,inner as te,offset as ne,shift as le,size as re,useFloating as oe,useInnerOffset as ie,useInteractions as se}from"@floating-ui/react";import*as j from"react";import{createContext as _,use<PERSON>allback as ae,useContext as b,useMemo as v,useRef as ue,useState as T}from"react";import{useDisposables as pe}from'../hooks/use-disposables.js';import{useEvent as z}from'../hooks/use-event.js';import{useIsoMorphicEffect as E}from'../hooks/use-iso-morphic-effect.js';let y=_({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});y.displayName="FloatingContext";let $=_(null);$.displayName="PlacementContext";function ye(){return b(y).setReference}function xe(){return b(y).getReferenceProps}function Fe(){let{getFloatingProps:e,slot:t}=b(y);return ae((...n)=>Object.assign({},e(...n),{"data-anchor":t.anchor}),[e,t])}function be(e){let t=b($),n=v(()=>e,[JSON.stringify(e,typeof HTMLElement!="undefined"?(s,a)=>a instanceof HTMLElement?a.outerHTML:a:void 0)]);E(()=>{t==null||t(n!=null?n:null)},[t,n]);let l=b(y);return v(()=>[l.setFloating,l.styles],[l.setFloating,l.styles])}let q=4;function Me({children:e,enabled:t=!0}){let[n,l]=T(null),[s,a]=T(0),M=ue(null),[m,o]=T(null);fe(m);let r=t&&n!==null&&m!==null,{to:x="bottom",gap:I=0,offset:R=0,padding:p=0,inner:d}=ce(n,m),[i,u="center"]=x.split(" ");E(()=>{r&&a(0)},[r]);let{refs:F,floatingStyles:w,context:f}=oe({open:r,placement:i==="selection"?u==="center"?"bottom":`bottom-${u}`:u==="center"?`${i}`:`${i}-${u}`,strategy:"fixed",transform:!1,middleware:[ne({mainAxis:i==="selection"?0:I,crossAxis:R}),le({padding:p}),i!=="selection"&&ee(),i==="selection"&&d?te({...d,padding:p,overflowRef:M,offset:s,minItemsVisible:q,referenceOverflowThreshold:p,onFallbackChange(P){var k,N;if(!P)return;let c=f.elements.floating;if(!c)return;let C=parseFloat(getComputedStyle(c).scrollPaddingBottom)||0,H=Math.min(q,c.childElementCount),B=0,O=0;for(let g of(N=(k=f.elements.floating)==null?void 0:k.childNodes)!=null?N:[])if(g instanceof HTMLElement){let h=g.offsetTop,U=h+g.clientHeight+C,S=c.scrollTop,W=S+c.clientHeight;if(h>=S&&U<=W)H--;else{O=Math.max(0,Math.min(U,W)-Math.max(h,S)),B=g.clientHeight;break}}H>=1&&a(g=>{let h=B*H-O+C;return g>=h?g:h})}}):null,re({apply({availableWidth:P,availableHeight:c,elements:C}){Object.assign(C.floating.style,{maxWidth:`${P-p}px`,maxHeight:`${c-p}px`})}})].filter(Boolean),whileElementsMounted:Z}),[A=i,V=u]=f.placement.split("-");i==="selection"&&(A="selection");let G=v(()=>({anchor:[A,V].filter(Boolean).join(" ")}),[A,V]),K=ie(f,{overflowRef:M,onChange:a}),{getReferenceProps:Q,getFloatingProps:X}=se([K]),Y=z(P=>{o(P),F.setFloating(P)});return j.createElement($.Provider,{value:l},j.createElement(y.Provider,{value:{setFloating:Y,setReference:F.setReference,styles:r?w:{},getReferenceProps:Q,getFloatingProps:X,slot:G}},e))}function fe(e){E(()=>{if(!e)return;let t=new MutationObserver(()=>{let n=e.style.maxHeight;parseFloat(n)!==parseInt(n)&&(e.style.maxHeight=`${Math.ceil(parseFloat(n))}px`)});return t.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{t.disconnect()}},[e])}function ce(e,t){let n=L(e==null?void 0:e.gap,t),l=L(e==null?void 0:e.offset,t),s=L(e==null?void 0:e.padding,t);return{...e,gap:n,offset:l,padding:s}}function L(e,t,n=void 0){let l=pe(),s=z((o,r)=>{if(o==null)return[n,null];if(typeof o=="number")return[o,null];if(typeof o=="string"){if(!r)return[n,null];let x=J(o,r);return[x,I=>{let R=D(o);{let p=R.map(d=>window.getComputedStyle(r).getPropertyValue(d));l.requestAnimationFrame(function d(){l.nextFrame(d);let i=!1;for(let[F,w]of R.entries()){let f=window.getComputedStyle(r).getPropertyValue(w);if(p[F]!==f){p[F]=f,i=!0;break}}if(!i)return;let u=J(o,r);x!==u&&(I(u),x=u)})}return l.dispose}]}return[n,null]}),a=v(()=>s(e,t)[0],[e,t]),[M=a,m]=T();return E(()=>{let[o,r]=s(e,t);if(m(o),!!r)return r(m)},[e,t]),M}function D(e){let t=/var\((.*)\)/.exec(e);if(t){let n=t[1].indexOf(",");if(n===-1)return[t[1]];let l=t[1].slice(0,n).trim(),s=t[1].slice(n+1).trim();return s?[l,...D(s)]:[l]}return[]}function J(e,t){let n=document.createElement("div");t.appendChild(n),n.style.setProperty("margin-top","0px","important"),n.style.setProperty("margin-top",e,"important");let l=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),l}export{Me as FloatingProvider,be as useFloatingPanel,Fe as useFloatingPanelProps,ye as useFloatingReference,xe as useFloatingReferenceProps};
