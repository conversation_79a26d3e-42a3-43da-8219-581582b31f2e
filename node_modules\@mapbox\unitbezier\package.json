{"name": "@mapbox/unitbezier", "version": "0.0.0", "description": "unit bezier curve interpolation", "main": "index.js", "scripts": {"test": "tap --coverage test/*.js"}, "repository": {"type": "git", "url": "**************:mapbox/unitbezier.git"}, "keywords": ["unit", "bezier", "interpolation", "webkit"], "author": "", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/mapbox/unitbezier/issues"}, "homepage": "https://github.com/mapbox/unitbezier", "devDependencies": {"cz-conventional-changelog": "1.2.0", "tap": "~9.0.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}