import u,{createContext as Pe,createRef as ye,use<PERSON><PERSON>back as K,useContext as V,useEffect as H,useMemo as y,useReducer as Ee,useRef as q,useState as Ae}from"react";import{FocusTrap as A}from'../../components/focus-trap/focus-trap.js';import{Portal as B,useNestedPortals as Re}from'../../components/portal/portal.js';import{useDocumentOverflowLockedEffect as Ce}from'../../hooks/document-overflow/use-document-overflow.js';import{useEvent as R}from'../../hooks/use-event.js';import{useEventListener as ve}from'../../hooks/use-event-listener.js';import{useId as C}from'../../hooks/use-id.js';import{useInert as z}from'../../hooks/use-inert.js';import{useOutsideClick as _e}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Oe}from'../../hooks/use-owner.js';import{useRootContainers as be}from'../../hooks/use-root-containers.js';import{useServerHandoffComplete as he}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as v}from'../../hooks/use-sync-refs.js';import{State as k,useOpenClosed as Se}from'../../internal/open-closed.js';import{ForcePortalRoot as G}from'../../internal/portal-force-root.js';import{StackMessage as Q,StackProvider as xe}from'../../internal/stack-context.js';import{isDisabledReactIssue7711 as Le}from'../../utils/bugs.js';import{match as N}from'../../utils/match.js';import{Features as Z,forwardRefWithAs as _,render as O}from'../../utils/render.js';import{Description as Fe,useDescriptions as ke}from'../description/description.js';import{Keys as Ie}from'../keyboard.js';var Me=(r=>(r[r.Open=0]="Open",r[r.Closed=1]="Closed",r))(Me||{}),we=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(we||{});let He={[0](t,e){return t.titleId===e.id?t:{...t,titleId:e.id}}},I=Pe(null);I.displayName="DialogContext";function b(t){let e=V(I);if(e===null){let r=new Error(`<${t} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,b),r}return e}function Be(t,e,r=()=>[document.body]){Ce(t,e,i=>{var n;return{containers:[...(n=i.containers)!=null?n:[],r]}})}function Ge(t,e){return N(e.type,He,t,e)}let Ne="div",Ue=Z.RenderStrategy|Z.Static;function We(t,e){let r=C(),{id:i=`headlessui-dialog-${r}`,open:n,onClose:l,initialFocus:s,role:a="dialog",__demoMode:T=!1,...m}=t,[M,f]=Ae(0),U=q(!1);a=function(){return a==="dialog"||a==="alertdialog"?a:(U.current||(U.current=!0,console.warn(`Invalid role [${a}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let E=Se();n===void 0&&E!==null&&(n=(E&k.Open)===k.Open);let D=q(null),ee=v(D,e),g=Oe(D),W=t.hasOwnProperty("open")||E!==null,$=t.hasOwnProperty("onClose");if(!W&&!$)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!W)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!$)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(typeof n!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${n}`);if(typeof l!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${l}`);let p=n?0:1,[h,te]=Ee(Ge,{titleId:null,descriptionId:null,panelRef:ye()}),P=R(()=>l(!1)),Y=R(o=>te({type:0,id:o})),S=he()?T?!1:p===0:!1,x=M>1,j=V(I)!==null,[oe,re]=Re(),ne={get current(){var o;return(o=h.panelRef.current)!=null?o:D.current}},{resolveContainers:w,mainTreeNodeRef:L,MainTreeNode:le}=be({portals:oe,defaultContainers:[ne]}),ae=x?"parent":"leaf",J=E!==null?(E&k.Closing)===k.Closing:!1,ie=(()=>j||J?!1:S)(),se=K(()=>{var o,c;return(c=Array.from((o=g==null?void 0:g.querySelectorAll("body > *"))!=null?o:[]).find(d=>d.id==="headlessui-portal-root"?!1:d.contains(L.current)&&d instanceof HTMLElement))!=null?c:null},[L]);z(se,ie);let pe=(()=>x?!0:S)(),de=K(()=>{var o,c;return(c=Array.from((o=g==null?void 0:g.querySelectorAll("[data-headlessui-portal]"))!=null?o:[]).find(d=>d.contains(L.current)&&d instanceof HTMLElement))!=null?c:null},[L]);z(de,pe);let ue=(()=>!(!S||x))();_e(w,P,ue);let fe=(()=>!(x||p!==0))();ve(g==null?void 0:g.defaultView,"keydown",o=>{fe&&(o.defaultPrevented||o.key===Ie.Escape&&(o.preventDefault(),o.stopPropagation(),P()))});let ge=(()=>!(J||p!==0||j))();Be(g,ge,w),H(()=>{if(p!==0||!D.current)return;let o=new ResizeObserver(c=>{for(let d of c){let F=d.target.getBoundingClientRect();F.x===0&&F.y===0&&F.width===0&&F.height===0&&P()}});return o.observe(D.current),()=>o.disconnect()},[p,D,P]);let[Te,ce]=ke(),De=y(()=>[{dialogState:p,close:P,setTitleId:Y},h],[p,h,P,Y]),X=y(()=>({open:p===0}),[p]),me={ref:ee,id:i,role:a,"aria-modal":p===0?!0:void 0,"aria-labelledby":h.titleId,"aria-describedby":Te};return u.createElement(xe,{type:"Dialog",enabled:p===0,element:D,onUpdate:R((o,c)=>{c==="Dialog"&&N(o,{[Q.Add]:()=>f(d=>d+1),[Q.Remove]:()=>f(d=>d-1)})})},u.createElement(G,{force:!0},u.createElement(B,null,u.createElement(I.Provider,{value:De},u.createElement(B.Group,{target:D},u.createElement(G,{force:!1},u.createElement(ce,{slot:X,name:"Dialog.Description"},u.createElement(A,{initialFocus:s,containers:w,features:S?N(ae,{parent:A.features.RestoreFocus,leaf:A.features.All&~A.features.FocusLock}):A.features.None},u.createElement(re,null,O({ourProps:me,theirProps:m,slot:X,defaultTag:Ne,features:Ue,visible:p===0,name:"Dialog"}))))))))),u.createElement(le,null))}let $e="div";function Ye(t,e){let r=C(),{id:i=`headlessui-dialog-overlay-${r}`,...n}=t,[{dialogState:l,close:s}]=b("Dialog.Overlay"),a=v(e),T=R(f=>{if(f.target===f.currentTarget){if(Le(f.currentTarget))return f.preventDefault();f.preventDefault(),f.stopPropagation(),s()}}),m=y(()=>({open:l===0}),[l]);return O({ourProps:{ref:a,id:i,"aria-hidden":!0,onClick:T},theirProps:n,slot:m,defaultTag:$e,name:"Dialog.Overlay"})}let je="div";function Je(t,e){let r=C(),{id:i=`headlessui-dialog-backdrop-${r}`,...n}=t,[{dialogState:l},s]=b("Dialog.Backdrop"),a=v(e);H(()=>{if(s.panelRef.current===null)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[s.panelRef]);let T=y(()=>({open:l===0}),[l]);return u.createElement(G,{force:!0},u.createElement(B,null,O({ourProps:{ref:a,id:i,"aria-hidden":!0},theirProps:n,slot:T,defaultTag:je,name:"Dialog.Backdrop"})))}let Xe="div";function Ke(t,e){let r=C(),{id:i=`headlessui-dialog-panel-${r}`,...n}=t,[{dialogState:l},s]=b("Dialog.Panel"),a=v(e,s.panelRef),T=y(()=>({open:l===0}),[l]),m=R(f=>{f.stopPropagation()});return O({ourProps:{ref:a,id:i,onClick:m},theirProps:n,slot:T,defaultTag:Xe,name:"Dialog.Panel"})}let Ve="h2";function qe(t,e){let r=C(),{id:i=`headlessui-dialog-title-${r}`,...n}=t,[{dialogState:l,setTitleId:s}]=b("Dialog.Title"),a=v(e);H(()=>(s(i),()=>s(null)),[i,s]);let T=y(()=>({open:l===0}),[l]);return O({ourProps:{ref:a,id:i},theirProps:n,slot:T,defaultTag:Ve,name:"Dialog.Title"})}let ze=_(We),Qe=_(Je),Ze=_(Ke),et=_(Ye),tt=_(qe),_t=Object.assign(ze,{Backdrop:Qe,Panel:Ze,Overlay:et,Title:tt,Description:Fe});export{_t as Dialog};
