"use client";import{useFocusRing as A}from"@react-aria/focus";import{useHover as E}from"@react-aria/interactions";import{useMemo as P}from"react";import{useId as F}from'../../hooks/use-id.js';import{useDisabled as R}from'../../internal/disabled.js';import{useProvidedId as g}from'../../internal/id.js';import{forwardRefWithAs as v,mergeProps as _,render as D}from'../../utils/render.js';import{useDescribedBy as h}from'../description/description.js';import{useLabelledBy as L}from'../label/label.js';let H="textarea";function X(a,i){var s;let n=F(),d=g(),p=R(),{id:T=d||`headlessui-textarea-${n}`,disabled:e=p||!1,invalid:o=!1,...f}=a,u=L(),m=h(),{isFocusVisible:r,focusProps:y}=A({autoFocus:(s=a.autoFocus)!=null?s:!1}),{isHovered:t,hoverProps:b}=E({isDisabled:e!=null?e:!1}),x=_({ref:i,id:T,"aria-labelledby":u,"aria-describedby":m,"aria-invalid":o?"":void 0,disabled:e||void 0},y,b),c=P(()=>{var l;return{disabled:e,invalid:o,hover:t,focus:r,autofocus:(l=a.autoFocus)!=null?l:!1}},[e,o,t,r,a.autoFocus]);return D({ourProps:x,theirProps:f,slot:c,defaultTag:H,name:"Textarea"})}let k=v(X);export{k as Textarea};
