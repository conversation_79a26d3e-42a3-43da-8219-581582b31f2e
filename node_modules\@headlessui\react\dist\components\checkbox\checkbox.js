"use client";import{useFocusRing as X}from"@react-aria/focus";import{useHover as G}from"@react-aria/interactions";import n,{useCallback as U,useMemo as S,useState as W}from"react";import{useActivePress as N}from'../../hooks/use-active-press.js';import{useControllable as J}from'../../hooks/use-controllable.js';import{useDisposables as V}from'../../hooks/use-disposables.js';import{useEvent as l}from'../../hooks/use-event.js';import{useId as $}from'../../hooks/use-id.js';import{useDisabled as j}from'../../internal/disabled.js';import{FormFields as q}from'../../internal/form-fields.js';import{useProvidedId as z}from'../../internal/id.js';import{isDisabledReactIssue7711 as T}from'../../utils/bugs.js';import{forwardRefWithAs as Q,mergeProps as Y,render as Z}from'../../utils/render.js';import{useDescribedBy as ee}from'../description/description.js';import{Keys as oe}from'../keyboard.js';import{useLabelledBy as te}from'../label/label.js';let re="span";function ae(a,h){var y;let C=$(),k=z(),x=j(),{id:g=k||`headlessui-checkbox-${C}`,disabled:e=x||!1,checked:E,defaultChecked:i=!1,onChange:v,name:d,value:P,form:D,indeterminate:s=!1,...F}=a,[r,t]=J(E,v,i),A=te(),R=ee(),_=V(),[c,p]=W(!1),u=l(()=>{p(!0),t==null||t(!r),_.nextFrame(()=>{p(!1)})}),K=l(o=>{if(T(o.currentTarget))return o.preventDefault();u()}),H=l(o=>{if(T(o.currentTarget))return o.preventDefault();switch(o.key){case oe.Space:o.preventDefault(),u();break}}),{isFocusVisible:f,focusProps:B}=X({autoFocus:(y=a.autoFocus)!=null?y:!1}),{isHovered:m,hoverProps:I}=G({isDisabled:e!=null?e:!1}),{pressed:b,pressProps:L}=N({disabled:e!=null?e:!1}),w=Y({ref:h,id:g,role:"checkbox","aria-checked":s?"mixed":r?"true":"false","aria-labelledby":A,"aria-describedby":R,"aria-disabled":e?!0:void 0,indeterminate:s?"true":void 0,tabIndex:0,onKeyDown:e?void 0:H,onClick:e?void 0:K},B,I,L),M=S(()=>{var o;return{checked:r,disabled:e,hover:m,focus:f,active:b,indeterminate:s,changing:c,autofocus:(o=a.autoFocus)!=null?o:!1}},[r,s,e,m,f,b,c,a.autoFocus]),O=U(()=>t==null?void 0:t(i),[t]);return n.createElement(n.Fragment,null,d!=null&&n.createElement(q,{data:r?{[d]:P||"on"}:{},form:D,onReset:O}),Z({ourProps:w,theirProps:F,slot:M,defaultTag:re,name:"Checkbox"}))}let xe=Q(ae);export{xe as Checkbox};
