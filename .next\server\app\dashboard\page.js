/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2l2YWhyJTVDJTVDT25lRHJpdmUlNUMlNUNEb2N1bWVudHMlNUMlNUNWQ29kZSUyMHByb2plY3RzJTVDJTVDemFrYXQtZGVlcGFnZW50JTVDJTVDemFrYXRfbWFuYWdlbWVudF9zeXN0ZW0lNUMlNUNhcHAlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQW1LIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLz8xOGViIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcaXZhaHJcXFxcT25lRHJpdmVcXFxcRG9jdW1lbnRzXFxcXFZDb2RlIHByb2plY3RzXFxcXHpha2F0LWRlZXBhZ2VudFxcXFx6YWthdF9tYW5hZ2VtZW50X3N5c3RlbVxcXFxhcHBcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/i18n-provider.tsx */ \"(ssr)/./providers/i18n-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/session-provider.tsx */ \"(ssr)/./providers/session-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Ci18n-provider.tsx%22%2C%22ids%22%3A%5B%22I18nProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Civahr%5C%5COneDrive%5C%5CDocuments%5C%5CVCode%20projects%5C%5Czakat-deepagent%5C%5Czakat_management_system%5C%5Capp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(ssr)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/stats-card */ \"(ssr)/./components/dashboard/stats-card.tsx\");\n/* harmony import */ var _components_dashboard_recent_requests__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/recent-requests */ \"(ssr)/./components/dashboard/recent-requests.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FileText,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _lib_mock_data__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/mock-data */ \"(ssr)/./lib/mock-data.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)() || {};\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    if (!session?.user) {\n        return null;\n    }\n    const userRole = session.user.role;\n    const stats = _lib_mock_data__WEBPACK_IMPORTED_MODULE_7__.mockDashboardStats[userRole];\n    // Get user-specific requests\n    const userRequests = userRole === \"zakat_applicant\" ? (0,_lib_mock_data__WEBPACK_IMPORTED_MODULE_7__.getRequestsByUserId)(session.user.id) : _lib_mock_data__WEBPACK_IMPORTED_MODULE_7__.mockAssistanceRequests.slice(0, 5);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_3__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: [\n                                t(\"welcome\"),\n                                \", \",\n                                session.user.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"نظرة عامة على حالة \",\n                                userRole === \"zakat_applicant\" ? \"طلباتك\" : \"المهام المخصصة لك\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                            title: t(\"total_requests\"),\n                            value: stats.totalRequests,\n                            description: \"إجمالي الطلبات\",\n                            icon: _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                            title: t(\"pending_review\"),\n                            value: stats.pendingReview,\n                            description: \"في انتظار المراجعة\",\n                            icon: _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                            title: t(\"approved_today\"),\n                            value: stats.approvedToday,\n                            description: \"موافق عليها اليوم\",\n                            icon: _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                            title: t(\"average_processing_days\"),\n                            value: stats.averageProcessingDays,\n                            description: \"متوسط أيام المعالجة\",\n                            icon: _barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recent_requests__WEBPACK_IMPORTED_MODULE_5__.RecentRequests, {\n                            requests: userRequests,\n                            showActions: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"إجراءات سريعة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                            children: \"الإجراءات الأكثر استخداماً\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: userRole === \"zakat_applicant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"طلب مساعدة جديد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 101,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"تقديم طلب مساعدة جديد\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 102,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-5 w-5 text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"تحديث الملف الشخصي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 110,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"تحديث البيانات الشخصية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 111,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"المهام المعلقة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: [\n                                                                        stats.pendingReview,\n                                                                        \" مهمة في الانتظار\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 123,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"التقارير\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"عرض التقارير والإحصائيات\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/recent-requests.tsx":
/*!**************************************************!*\
  !*** ./components/dashboard/recent-requests.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentRequests: () => (/* binding */ RecentRequests)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _ui_status_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/status-badge */ \"(ssr)/./components/ui/status-badge.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(ssr)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/locale/ar.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ RecentRequests auto */ \n\n\n\n\n\n\n\n\nfunction RecentRequests({ requests, showActions = true }) {\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const formatDate = (date)=>{\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yyyy\", {\n            locale: i18n.language === \"ar\" ? date_fns_locale__WEBPACK_IMPORTED_MODULE_7__.ar : undefined\n        });\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(i18n.language === \"ar\" ? \"ar-SA\" : \"en-US\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            maximumFractionDigits: 0\n        }).format(amount);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        t(\"requests\"),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-normal text-muted-foreground\",\n                            children: [\n                                \"(\",\n                                requests.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: requests.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-muted-foreground py-8\",\n                        children: \"لا توجد طلبات حالياً\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, this) : requests.map((request)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium\",\n                                                    children: i18n.language === \"ar\" ? request.assistanceType.nameAr : request.assistanceType.nameEn\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_status_badge__WEBPACK_IMPORTED_MODULE_2__.StatusBadge, {\n                                                    status: request.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: formatDate(request.submissionDate)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: formatCurrency(request.requestedAmount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground line-clamp-2\",\n                                            children: request.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 17\n                                }, this),\n                                showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: `/requests/${request.id}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, request.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-requests.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/recent-requests.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/stats-card.tsx":
/*!*********************************************!*\
  !*** ./components/dashboard/stats-card.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCard: () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ StatsCard auto */ \n\n\n\nfunction StatsCard({ title, value, description, icon: Icon, trend }) {\n    const [displayValue, setDisplayValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const duration = 1000 // Animation duration in ms\n        ;\n        const increment = value / (duration / 16 // 60 FPS\n        );\n        let current = 0;\n        const timer = setInterval(()=>{\n            current += increment;\n            if (current >= value) {\n                setDisplayValue(value);\n                clearInterval(timer);\n            } else {\n                setDisplayValue(Math.floor(current));\n            }\n        }, 16);\n        return ()=>clearInterval(timer);\n    }, [\n        value\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.3\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n            className: \"hover:shadow-md transition-shadow\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"h-4 w-4 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold\",\n                            children: displayValue.toLocaleString()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground mt-1\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this),\n                        trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-xs mt-2 flex items-center ${trend.isPositive ? \"text-green-600\" : \"text-red-600\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: trend.isPositive ? \"↗\" : \"↘\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-1\",\n                                    children: [\n                                        Math.abs(trend.value),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\stats-card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/stats-card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/dashboard-layout.tsx":
/*!************************************************!*\
  !*** ./components/layout/dashboard-layout.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./navbar */ \"(ssr)/./components/layout/navbar.tsx\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./components/layout/sidebar.tsx\");\n/* harmony import */ var _ui_toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/toaster */ \"(ssr)/./components/ui/toaster.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)() || {};\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session?.user) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/auth/login\");\n    }\n    // Check account status\n    if (session.user.accountStatus !== \"active\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"حسابك في انتظار الموافقة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"يرجى انتظار موافقة الإدارة على حسابك\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navbar__WEBPACK_IMPORTED_MODULE_3__.Navbar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto bg-muted/30 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-6xl\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_toaster__WEBPACK_IMPORTED_MODULE_5__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/navbar.tsx":
/*!**************************************!*\
  !*** ./components/layout/navbar.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _ui_language_switcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/language-switcher */ \"(ssr)/./components/ui/language-switcher.tsx\");\n/* harmony import */ var _ui_role_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/role-badge */ \"(ssr)/./components/ui/role-badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../ui/avatar */ \"(ssr)/./components/ui/avatar.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\n\n\n\n\n\n\nfunction Navbar() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)() || {};\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    if (!session?.user) {\n        return null;\n    }\n    const handleLogout = ()=>{\n        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n            callbackUrl: \"/auth/login\"\n        });\n    };\n    const userInitials = session.user.name?.split(\" \").map((n)=>n[0]).join(\"\").toUpperCase();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container flex h-16 items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    href: \"/dashboard\",\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 rounded bg-primary flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-primary-foreground font-bold text-sm\",\n                                children: \"ز\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                t(\"dashboard\"),\n                                \" - نظام إدارة الزكاة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_language_switcher__WEBPACK_IMPORTED_MODULE_4__.LanguageSwitcher, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"relative h-9 w-9 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                            className: \"h-9 w-9\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: userInitials || \"U\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                    className: \"w-56\",\n                                    align: \"end\",\n                                    forceMount: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col space-y-1 p-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium leading-none\",\n                                                    children: session.user.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs leading-none text-muted-foreground\",\n                                                    children: session.user.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_role_badge__WEBPACK_IMPORTED_MODULE_5__.RoleBadge, {\n                                                        role: session.user.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                href: \"/profile\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: t(\"profile\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                href: \"/settings\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: t(\"settings\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                            onClick: handleLogout,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t(\"logout\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\navbar.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/sidebar.tsx":
/*!***************************************!*\
  !*** ./components/layout/sidebar.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardList,CreditCard,FileText,LayoutDashboard,Settings,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardList,CreditCard,FileText,LayoutDashboard,Settings,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardList,CreditCard,FileText,LayoutDashboard,Settings,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardList,CreditCard,FileText,LayoutDashboard,Settings,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardList,CreditCard,FileText,LayoutDashboard,Settings,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardList,CreditCard,FileText,LayoutDashboard,Settings,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardList,CreditCard,FileText,LayoutDashboard,Settings,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardList,CreditCard,FileText,LayoutDashboard,Settings,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\nconst sidebarItems = [\n    {\n        title: \"dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        title: \"profile\",\n        href: \"/profile\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        roles: [\n            \"zakat_applicant\"\n        ]\n    },\n    {\n        title: \"requests\",\n        href: \"/requests\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        title: \"Beneficiaries\",\n        href: \"/beneficiaries\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        roles: [\n            \"reception_staff\",\n            \"researcher\",\n            \"department_head\",\n            \"admin_manager\",\n            \"minister\",\n            \"system_admin\"\n        ]\n    },\n    {\n        title: \"tasks\",\n        href: \"/tasks\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        roles: [\n            \"reception_staff\",\n            \"researcher\",\n            \"banking_expert\",\n            \"department_head\",\n            \"admin_manager\",\n            \"minister\"\n        ]\n    },\n    {\n        title: \"reports\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        roles: [\n            \"reception_staff\",\n            \"researcher\",\n            \"banking_expert\",\n            \"department_head\",\n            \"admin_manager\",\n            \"minister\",\n            \"system_admin\"\n        ]\n    },\n    {\n        title: \"User Management\",\n        href: \"/admin/users\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        roles: [\n            \"system_admin\"\n        ]\n    },\n    {\n        title: \"Assistance Types\",\n        href: \"/admin/assistance-types\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        roles: [\n            \"system_admin\"\n        ]\n    },\n    {\n        title: \"System Settings\",\n        href: \"/admin/settings\",\n        icon: _barrel_optimize_names_BarChart3_ClipboardList_CreditCard_FileText_LayoutDashboard_Settings_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        roles: [\n            \"system_admin\"\n        ]\n    }\n];\nfunction Sidebar() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)() || {};\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    if (!session?.user) {\n        return null;\n    }\n    const filteredItems = sidebarItems.filter((item)=>!item.roles || item.roles.includes(session.user.role));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen w-64 flex-col border-r bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 space-y-2 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: item.href,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors\", pathname === item.href ? \"bg-accent text-accent-foreground\" : \"text-muted-foreground\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            t(item.title)\n                        ]\n                    }, item.href, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\sidebar.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\layout\\\\sidebar.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FwcC8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarImage,AvatarFallback auto */ \n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/language-switcher.tsx":
/*!*********************************************!*\
  !*** ./components/ui/language-switcher.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageSwitcher: () => (/* binding */ LanguageSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ LanguageSwitcher auto */ \n\n\n\n\n\nfunction LanguageSwitcher() {\n    const { i18n, t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const changeLanguage = (lng)=>{\n        i18n.changeLanguage(lng);\n        // Update HTML direction\n        document.documentElement.dir = lng === \"ar\" ? \"rtl\" : \"ltr\";\n        document.documentElement.lang = lng;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        // Set initial direction based on current language\n        document.documentElement.dir = i18n.language === \"ar\" ? \"rtl\" : \"ltr\";\n        document.documentElement.lang = i18n.language;\n    }, [\n        i18n.language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-4 w-4 me-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        i18n.language === \"ar\" ? \"العربية\" : \"English\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                align: \"end\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>changeLanguage(\"ar\"),\n                        children: \"العربية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                        onClick: ()=>changeLanguage(\"en\"),\n                        children: \"English\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\language-switcher.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/language-switcher.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/role-badge.tsx":
/*!**************************************!*\
  !*** ./components/ui/role-badge.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoleBadge: () => (/* binding */ RoleBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ RoleBadge auto */ \n\n\nconst roleColors = {\n    zakat_applicant: \"default\",\n    reception_staff: \"secondary\",\n    researcher: \"outline\",\n    banking_expert: \"destructive\",\n    department_head: \"default\",\n    admin_manager: \"secondary\",\n    minister: \"outline\",\n    system_admin: \"destructive\"\n};\nfunction RoleBadge({ role }) {\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n        variant: roleColors[role],\n        children: t(role)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\role-badge.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3JvbGUtYmFkZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUcrQjtBQUNlO0FBTzlDLE1BQU1FLGFBQWE7SUFDakJDLGlCQUFpQjtJQUNqQkMsaUJBQWlCO0lBQ2pCQyxZQUFZO0lBQ1pDLGdCQUFnQjtJQUNoQkMsaUJBQWlCO0lBQ2pCQyxlQUFlO0lBQ2ZDLFVBQVU7SUFDVkMsY0FBYztBQUNoQjtBQUVPLFNBQVNDLFVBQVUsRUFBRUMsSUFBSSxFQUFrQjtJQUNoRCxNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHWiw2REFBY0E7SUFFNUIscUJBQ0UsOERBQUNELHlDQUFLQTtRQUFDYyxTQUFTWixVQUFVLENBQUNVLEtBQUs7a0JBQzdCQyxFQUFFRDs7Ozs7O0FBR1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9jb21wb25lbnRzL3VpL3JvbGUtYmFkZ2UudHN4PzFjNzYiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICcuL2JhZGdlJ1xuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICdyZWFjdC1pMThuZXh0J1xuaW1wb3J0IHR5cGUgeyBVc2VyUm9sZSB9IGZyb20gJ0AvbGliL3R5cGVzJ1xuXG5pbnRlcmZhY2UgUm9sZUJhZGdlUHJvcHMge1xuICByb2xlOiBVc2VyUm9sZVxufVxuXG5jb25zdCByb2xlQ29sb3JzID0ge1xuICB6YWthdF9hcHBsaWNhbnQ6ICdkZWZhdWx0JyxcbiAgcmVjZXB0aW9uX3N0YWZmOiAnc2Vjb25kYXJ5JyxcbiAgcmVzZWFyY2hlcjogJ291dGxpbmUnLFxuICBiYW5raW5nX2V4cGVydDogJ2Rlc3RydWN0aXZlJyxcbiAgZGVwYXJ0bWVudF9oZWFkOiAnZGVmYXVsdCcsXG4gIGFkbWluX21hbmFnZXI6ICdzZWNvbmRhcnknLFxuICBtaW5pc3RlcjogJ291dGxpbmUnLFxuICBzeXN0ZW1fYWRtaW46ICdkZXN0cnVjdGl2ZScsXG59IGFzIGNvbnN0XG5cbmV4cG9ydCBmdW5jdGlvbiBSb2xlQmFkZ2UoeyByb2xlIH06IFJvbGVCYWRnZVByb3BzKSB7XG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKVxuICBcbiAgcmV0dXJuIChcbiAgICA8QmFkZ2UgdmFyaWFudD17cm9sZUNvbG9yc1tyb2xlXX0+XG4gICAgICB7dChyb2xlKX1cbiAgICA8L0JhZGdlPlxuICApXG59XG4iXSwibmFtZXMiOlsiQmFkZ2UiLCJ1c2VUcmFuc2xhdGlvbiIsInJvbGVDb2xvcnMiLCJ6YWthdF9hcHBsaWNhbnQiLCJyZWNlcHRpb25fc3RhZmYiLCJyZXNlYXJjaGVyIiwiYmFua2luZ19leHBlcnQiLCJkZXBhcnRtZW50X2hlYWQiLCJhZG1pbl9tYW5hZ2VyIiwibWluaXN0ZXIiLCJzeXN0ZW1fYWRtaW4iLCJSb2xlQmFkZ2UiLCJyb2xlIiwidCIsInZhcmlhbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/role-badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/status-badge.tsx":
/*!****************************************!*\
  !*** ./components/ui/status-badge.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatusBadge: () => (/* binding */ StatusBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Eye,FileText,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Eye,FileText,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Eye,FileText,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Eye,FileText,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Eye,FileText,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Eye,FileText,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Eye,FileText,RefreshCw,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ StatusBadge auto */ \n\n\n\nconst statusConfig = {\n    draft: {\n        variant: \"secondary\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    submitted: {\n        variant: \"default\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    reception_review: {\n        variant: \"default\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    researcher_review: {\n        variant: \"default\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    banking_expert_review: {\n        variant: \"default\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    department_head_review: {\n        variant: \"default\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    admin_manager_review: {\n        variant: \"default\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    minister_review: {\n        variant: \"default\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    approved: {\n        variant: \"default\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    rejected: {\n        variant: \"destructive\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    needs_more_info: {\n        variant: \"outline\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    returned: {\n        variant: \"secondary\",\n        icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Eye_FileText_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n};\nfunction StatusBadge({ status, showIcon = true }) {\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const config = statusConfig[status];\n    const Icon = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n        variant: config.variant,\n        className: \"gap-1\",\n        children: [\n            showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                className: \"h-3 w-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\status-badge.tsx\",\n                lineNumber: 44,\n                columnNumber: 20\n            }, this),\n            t(status)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\status-badge.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3N0YXR1cy1iYWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUcrQjtBQUNlO0FBVXpCO0FBT3JCLE1BQU1TLGVBQWU7SUFDbkJDLE9BQU87UUFBRUMsU0FBUztRQUFzQkMsTUFBTVAsMElBQVFBO0lBQUM7SUFDdkRRLFdBQVc7UUFBRUYsU0FBUztRQUFvQkMsTUFBTVYsMElBQUtBO0lBQUM7SUFDdERZLGtCQUFrQjtRQUFFSCxTQUFTO1FBQW9CQyxNQUFNTiwwSUFBR0E7SUFBQztJQUMzRFMsbUJBQW1CO1FBQUVKLFNBQVM7UUFBb0JDLE1BQU1OLDBJQUFHQTtJQUFDO0lBQzVEVSx1QkFBdUI7UUFBRUwsU0FBUztRQUFvQkMsTUFBTU4sMElBQUdBO0lBQUM7SUFDaEVXLHdCQUF3QjtRQUFFTixTQUFTO1FBQW9CQyxNQUFNTiwwSUFBR0E7SUFBQztJQUNqRVksc0JBQXNCO1FBQUVQLFNBQVM7UUFBb0JDLE1BQU1OLDBJQUFHQTtJQUFDO0lBQy9EYSxpQkFBaUI7UUFBRVIsU0FBUztRQUFvQkMsTUFBTU4sMElBQUdBO0lBQUM7SUFDMURjLFVBQVU7UUFBRVQsU0FBUztRQUFvQkMsTUFBTVQsMElBQVdBO0lBQUM7SUFDM0RrQixVQUFVO1FBQUVWLFNBQVM7UUFBd0JDLE1BQU1SLDBJQUFPQTtJQUFDO0lBQzNEa0IsaUJBQWlCO1FBQUVYLFNBQVM7UUFBb0JDLE1BQU1KLDBJQUFhQTtJQUFDO0lBQ3BFZSxVQUFVO1FBQUVaLFNBQVM7UUFBc0JDLE1BQU1MLDBJQUFTQTtJQUFDO0FBQzdEO0FBRU8sU0FBU2lCLFlBQVksRUFBRUMsTUFBTSxFQUFFQyxXQUFXLElBQUksRUFBb0I7SUFDdkUsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBRzFCLDZEQUFjQTtJQUM1QixNQUFNMkIsU0FBU25CLFlBQVksQ0FBQ2dCLE9BQU87SUFDbkMsTUFBTUksT0FBT0QsT0FBT2hCLElBQUk7SUFFeEIscUJBQ0UsOERBQUNaLHlDQUFLQTtRQUFDVyxTQUFTaUIsT0FBT2pCLE9BQU87UUFBRW1CLFdBQVU7O1lBQ3ZDSiwwQkFBWSw4REFBQ0c7Z0JBQUtDLFdBQVU7Ozs7OztZQUM1QkgsRUFBRUY7Ozs7Ozs7QUFHVCIsInNvdXJjZXMiOlsid2VicGFjazovL2FwcC8uL2NvbXBvbmVudHMvdWkvc3RhdHVzLWJhZGdlLnRzeD9lNDE4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnLi9iYWRnZSdcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAncmVhY3QtaTE4bmV4dCdcbmltcG9ydCB0eXBlIHsgUmVxdWVzdFN0YXR1cyB9IGZyb20gJ0AvbGliL3R5cGVzJ1xuaW1wb3J0IHsgXG4gIENsb2NrLCBcbiAgQ2hlY2tDaXJjbGUsIFxuICBYQ2lyY2xlLCBcbiAgRmlsZVRleHQsIFxuICBFeWUsIFxuICBSZWZyZXNoQ3csXG4gIEFsZXJ0VHJpYW5nbGUgXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIFN0YXR1c0JhZGdlUHJvcHMge1xuICBzdGF0dXM6IFJlcXVlc3RTdGF0dXNcbiAgc2hvd0ljb24/OiBib29sZWFuXG59XG5cbmNvbnN0IHN0YXR1c0NvbmZpZyA9IHtcbiAgZHJhZnQ6IHsgdmFyaWFudDogJ3NlY29uZGFyeScgYXMgY29uc3QsIGljb246IEZpbGVUZXh0IH0sXG4gIHN1Ym1pdHRlZDogeyB2YXJpYW50OiAnZGVmYXVsdCcgYXMgY29uc3QsIGljb246IENsb2NrIH0sXG4gIHJlY2VwdGlvbl9yZXZpZXc6IHsgdmFyaWFudDogJ2RlZmF1bHQnIGFzIGNvbnN0LCBpY29uOiBFeWUgfSxcbiAgcmVzZWFyY2hlcl9yZXZpZXc6IHsgdmFyaWFudDogJ2RlZmF1bHQnIGFzIGNvbnN0LCBpY29uOiBFeWUgfSxcbiAgYmFua2luZ19leHBlcnRfcmV2aWV3OiB7IHZhcmlhbnQ6ICdkZWZhdWx0JyBhcyBjb25zdCwgaWNvbjogRXllIH0sXG4gIGRlcGFydG1lbnRfaGVhZF9yZXZpZXc6IHsgdmFyaWFudDogJ2RlZmF1bHQnIGFzIGNvbnN0LCBpY29uOiBFeWUgfSxcbiAgYWRtaW5fbWFuYWdlcl9yZXZpZXc6IHsgdmFyaWFudDogJ2RlZmF1bHQnIGFzIGNvbnN0LCBpY29uOiBFeWUgfSxcbiAgbWluaXN0ZXJfcmV2aWV3OiB7IHZhcmlhbnQ6ICdkZWZhdWx0JyBhcyBjb25zdCwgaWNvbjogRXllIH0sXG4gIGFwcHJvdmVkOiB7IHZhcmlhbnQ6ICdkZWZhdWx0JyBhcyBjb25zdCwgaWNvbjogQ2hlY2tDaXJjbGUgfSxcbiAgcmVqZWN0ZWQ6IHsgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyBhcyBjb25zdCwgaWNvbjogWENpcmNsZSB9LFxuICBuZWVkc19tb3JlX2luZm86IHsgdmFyaWFudDogJ291dGxpbmUnIGFzIGNvbnN0LCBpY29uOiBBbGVydFRyaWFuZ2xlIH0sXG4gIHJldHVybmVkOiB7IHZhcmlhbnQ6ICdzZWNvbmRhcnknIGFzIGNvbnN0LCBpY29uOiBSZWZyZXNoQ3cgfSxcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFN0YXR1c0JhZGdlKHsgc3RhdHVzLCBzaG93SWNvbiA9IHRydWUgfTogU3RhdHVzQmFkZ2VQcm9wcykge1xuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKClcbiAgY29uc3QgY29uZmlnID0gc3RhdHVzQ29uZmlnW3N0YXR1c11cbiAgY29uc3QgSWNvbiA9IGNvbmZpZy5pY29uXG4gIFxuICByZXR1cm4gKFxuICAgIDxCYWRnZSB2YXJpYW50PXtjb25maWcudmFyaWFudH0gY2xhc3NOYW1lPVwiZ2FwLTFcIj5cbiAgICAgIHtzaG93SWNvbiAmJiA8SWNvbiBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz59XG4gICAgICB7dChzdGF0dXMpfVxuICAgIDwvQmFkZ2U+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJCYWRnZSIsInVzZVRyYW5zbGF0aW9uIiwiQ2xvY2siLCJDaGVja0NpcmNsZSIsIlhDaXJjbGUiLCJGaWxlVGV4dCIsIkV5ZSIsIlJlZnJlc2hDdyIsIkFsZXJ0VHJpYW5nbGUiLCJzdGF0dXNDb25maWciLCJkcmFmdCIsInZhcmlhbnQiLCJpY29uIiwic3VibWl0dGVkIiwicmVjZXB0aW9uX3JldmlldyIsInJlc2VhcmNoZXJfcmV2aWV3IiwiYmFua2luZ19leHBlcnRfcmV2aWV3IiwiZGVwYXJ0bWVudF9oZWFkX3JldmlldyIsImFkbWluX21hbmFnZXJfcmV2aWV3IiwibWluaXN0ZXJfcmV2aWV3IiwiYXBwcm92ZWQiLCJyZWplY3RlZCIsIm5lZWRzX21vcmVfaW5mbyIsInJldHVybmVkIiwiU3RhdHVzQmFkZ2UiLCJzdGF0dXMiLCJzaG93SWNvbiIsInQiLCJjb25maWciLCJJY29uIiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/status-badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(ssr)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            \"under_review\": \"قيد المراجعة\",\n            \"approved\": \"موافق عليه\",\n            \"rejected\": \"مرفوض\",\n            \"pending\": \"في الانتظار\",\n            \"completed\": \"مكتمل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"welcome\": \"مرحباً\",\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"pending\": \"Pending\",\n            \"completed\": \"Completed\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"welcome\": \"Welcome\",\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./lib/mock-data.ts":
/*!**************************!*\
  !*** ./lib/mock-data.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBeneficiariesByCategory: () => (/* binding */ getBeneficiariesByCategory),\n/* harmony export */   getBeneficiariesByStatus: () => (/* binding */ getBeneficiariesByStatus),\n/* harmony export */   getBeneficiaryById: () => (/* binding */ getBeneficiaryById),\n/* harmony export */   getBeneficiaryStats: () => (/* binding */ getBeneficiaryStats),\n/* harmony export */   getRequestsByUserId: () => (/* binding */ getRequestsByUserId),\n/* harmony export */   getTasksByUserId: () => (/* binding */ getTasksByUserId),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   mockAssistanceRequests: () => (/* binding */ mockAssistanceRequests),\n/* harmony export */   mockAssistanceTypes: () => (/* binding */ mockAssistanceTypes),\n/* harmony export */   mockBeneficiaries: () => (/* binding */ mockBeneficiaries),\n/* harmony export */   mockDashboardStats: () => (/* binding */ mockDashboardStats),\n/* harmony export */   mockTasks: () => (/* binding */ mockTasks),\n/* harmony export */   mockUsers: () => (/* binding */ mockUsers),\n/* harmony export */   searchBeneficiaries: () => (/* binding */ searchBeneficiaries),\n/* harmony export */   zakatCategoryLabels: () => (/* binding */ zakatCategoryLabels)\n/* harmony export */ });\n// Mock Users for each role\nconst mockUsers = [\n    // Test admin account\n    {\n        id: \"1\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"John Doe\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1980-01-01\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"system_admin\",\n        createdAt: new Date(\"2023-01-01\"),\n        lastLogin: new Date()\n    },\n    // Zakat Applicants\n    {\n        id: \"2\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Ahmed Salem Al-Rashid\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1985-05-15\"),\n        phoneNumber: \"+************\",\n        address: \"Jeddah, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"zakat_applicant\",\n        createdAt: new Date(\"2023-02-01\"),\n        lastLogin: new Date(),\n        profileId: \"profile-2\"\n    },\n    {\n        id: \"3\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Fatima Omar Al-Zahra\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1990-08-20\"),\n        phoneNumber: \"+************\",\n        address: \"Dammam, Saudi Arabia\",\n        accountStatus: \"pending_approval\",\n        role: \"zakat_applicant\",\n        createdAt: new Date(\"2023-03-01\"),\n        profileId: \"profile-3\"\n    },\n    // Staff Members\n    {\n        id: \"4\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Sara Abdullah Al-Mansouri\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1988-03-10\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"reception_staff\",\n        createdAt: new Date(\"2022-01-15\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"5\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Mohammed Hassan Al-Qadiri\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1982-07-25\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"researcher\",\n        createdAt: new Date(\"2022-02-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"6\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Khalid Ahmed Al-Othman\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1975-12-05\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"banking_expert\",\n        createdAt: new Date(\"2021-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"7\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Nasser Fahad Al-Saud\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1970-04-18\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"department_head\",\n        createdAt: new Date(\"2020-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"8\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"Abdulaziz Mohammed Al-Rashid\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1968-09-12\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"admin_manager\",\n        createdAt: new Date(\"2019-01-01\"),\n        lastLogin: new Date()\n    },\n    {\n        id: \"9\",\n        nationalId: \"**********\",\n        email: \"<EMAIL>\",\n        fullName: \"His Excellency Abdullah bin Salman\",\n        nationality: \"Saudi Arabia\",\n        dateOfBirth: new Date(\"1965-02-28\"),\n        phoneNumber: \"+************\",\n        address: \"Riyadh, Saudi Arabia\",\n        accountStatus: \"active\",\n        role: \"minister\",\n        createdAt: new Date(\"2018-01-01\"),\n        lastLogin: new Date()\n    }\n];\n// Mock Assistance Types\nconst mockAssistanceTypes = [\n    {\n        id: \"financial-support\",\n        nameAr: \"المساعدة المالية العامة\",\n        nameEn: \"General Financial Support\",\n        descriptionAr: \"مساعدة مالية للأسر المحتاجة\",\n        descriptionEn: \"Financial assistance for needy families\",\n        maxAmount: 25000,\n        requiredDocuments: [\n            {\n                id: \"salary-certificate\",\n                nameAr: \"شهادة راتب\",\n                nameEn: \"Salary Certificate\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 2048\n            },\n            {\n                id: \"bank-statement\",\n                nameAr: \"كشف حساب بنكي\",\n                nameEn: \"Bank Statement\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\"\n                ],\n                maxSizeKB: 5120\n            }\n        ],\n        eligibilityCriteria: [\n            {\n                field: \"monthlyIncome\",\n                condition: \"less_than\",\n                value: 5000,\n                nationality: \"Saudi Arabia\"\n            }\n        ],\n        isActive: true,\n        category: \"Financial\"\n    },\n    {\n        id: \"medical-support\",\n        nameAr: \"المساعدة الطبية\",\n        nameEn: \"Medical Support\",\n        descriptionAr: \"مساعدة لتغطية التكاليف الطبية\",\n        descriptionEn: \"Assistance to cover medical expenses\",\n        maxAmount: 50000,\n        requiredDocuments: [\n            {\n                id: \"medical-report\",\n                nameAr: \"التقرير الطبي\",\n                nameEn: \"Medical Report\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 3072\n            },\n            {\n                id: \"medical-bills\",\n                nameAr: \"الفواتير الطبية\",\n                nameEn: \"Medical Bills\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 5120\n            }\n        ],\n        eligibilityCriteria: [],\n        isActive: true,\n        category: \"Medical\"\n    },\n    {\n        id: \"education-support\",\n        nameAr: \"المساعدة التعليمية\",\n        nameEn: \"Education Support\",\n        descriptionAr: \"مساعدة لتغطية تكاليف التعليم\",\n        descriptionEn: \"Assistance to cover education expenses\",\n        maxAmount: 15000,\n        requiredDocuments: [\n            {\n                id: \"enrollment-certificate\",\n                nameAr: \"شهادة قيد\",\n                nameEn: \"Enrollment Certificate\",\n                isRequired: true,\n                acceptedFormats: [\n                    \"pdf\",\n                    \"jpg\",\n                    \"png\"\n                ],\n                maxSizeKB: 2048\n            }\n        ],\n        eligibilityCriteria: [\n            {\n                field: \"familyMembersCount\",\n                condition: \"greater_than\",\n                value: 0\n            }\n        ],\n        isActive: true,\n        category: \"Education\"\n    }\n];\n// Mock Assistance Requests\nconst mockAssistanceRequests = [\n    {\n        id: \"req-001\",\n        userId: \"2\",\n        assistanceType: mockAssistanceTypes[0],\n        requestedAmount: 15000,\n        approvedAmount: 12000,\n        description: \"نحتاج إلى مساعدة مالية لتغطية تكاليف المعيشة بعد توقف العمل\",\n        status: \"approved\",\n        submissionDate: new Date(\"2023-10-15\"),\n        lastUpdateDate: new Date(\"2023-11-01\"),\n        attachedDocuments: [],\n        workflow: [\n            {\n                id: \"step-1\",\n                requestId: \"req-001\",\n                stage: \"reception_review\",\n                reviewerId: \"4\",\n                reviewerName: \"Sara Abdullah Al-Mansouri\",\n                stageStartDate: new Date(\"2023-10-15\"),\n                stageEndDate: new Date(\"2023-10-16\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-10-16\"),\n                notes: \"المستندات كاملة والحالة تستدعي المساعدة\"\n            },\n            {\n                id: \"step-2\",\n                requestId: \"req-001\",\n                stage: \"approved\",\n                reviewerId: \"8\",\n                reviewerName: \"Abdulaziz Mohammed Al-Rashid\",\n                stageStartDate: new Date(\"2023-10-30\"),\n                stageEndDate: new Date(\"2023-11-01\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-11-01\"),\n                notes: \"تمت الموافقة على مبلغ 12,000 ريال\"\n            }\n        ],\n        priority: \"medium\"\n    },\n    {\n        id: \"req-002\",\n        userId: \"3\",\n        assistanceType: mockAssistanceTypes[1],\n        requestedAmount: 35000,\n        description: \"نحتاج مساعدة لتغطية تكاليف علاج والدي في المستشفى\",\n        status: \"researcher_review\",\n        submissionDate: new Date(\"2023-11-10\"),\n        lastUpdateDate: new Date(\"2023-11-12\"),\n        attachedDocuments: [],\n        workflow: [\n            {\n                id: \"step-1\",\n                requestId: \"req-002\",\n                stage: \"reception_review\",\n                reviewerId: \"4\",\n                reviewerName: \"Sara Abdullah Al-Mansouri\",\n                stageStartDate: new Date(\"2023-11-10\"),\n                stageEndDate: new Date(\"2023-11-11\"),\n                decision: \"approve\",\n                decisionDate: new Date(\"2023-11-11\"),\n                notes: \"تم مراجعة الطلب وإحالته للباحث\"\n            },\n            {\n                id: \"step-2\",\n                requestId: \"req-002\",\n                stage: \"researcher_review\",\n                reviewerId: \"5\",\n                reviewerName: \"Mohammed Hassan Al-Qadiri\",\n                stageStartDate: new Date(\"2023-11-11\"),\n                notes: \"قيد المراجعة والتحقق من المستندات الطبية\"\n            }\n        ],\n        priority: \"high\"\n    }\n];\n// Mock Tasks for different roles\nconst mockTasks = {\n    reception_staff: [\n        {\n            id: \"task-1\",\n            assignedTo: \"4\",\n            requestId: \"req-003\",\n            type: \"profile_review\",\n            priority: \"medium\",\n            dueDate: new Date(Date.now() + ********),\n            status: \"pending\",\n            createdDate: new Date()\n        },\n        {\n            id: \"task-2\",\n            assignedTo: \"4\",\n            requestId: \"req-004\",\n            type: \"request_review\",\n            priority: \"high\",\n            status: \"in_progress\",\n            createdDate: new Date(Date.now() - ********)\n        }\n    ],\n    researcher: [\n        {\n            id: \"task-3\",\n            assignedTo: \"5\",\n            requestId: \"req-002\",\n            type: \"request_review\",\n            priority: \"high\",\n            dueDate: new Date(Date.now() + *********),\n            status: \"in_progress\",\n            createdDate: new Date(\"2023-11-11\")\n        }\n    ],\n    banking_expert: [\n        {\n            id: \"task-4\",\n            assignedTo: \"6\",\n            requestId: \"req-005\",\n            type: \"request_review\",\n            priority: \"medium\",\n            dueDate: new Date(Date.now() + *********),\n            status: \"pending\",\n            createdDate: new Date()\n        }\n    ],\n    department_head: [],\n    admin_manager: [],\n    minister: [],\n    zakat_applicant: [],\n    system_admin: []\n};\n// Mock Dashboard Stats for different roles\nconst mockDashboardStats = {\n    reception_staff: {\n        totalRequests: 45,\n        pendingReview: 8,\n        approvedToday: 3,\n        rejectedToday: 1,\n        averageProcessingDays: 2,\n        totalUsers: 150\n    },\n    researcher: {\n        totalRequests: 32,\n        pendingReview: 5,\n        approvedToday: 2,\n        rejectedToday: 0,\n        averageProcessingDays: 3,\n        totalUsers: 150\n    },\n    banking_expert: {\n        totalRequests: 28,\n        pendingReview: 4,\n        approvedToday: 1,\n        rejectedToday: 1,\n        averageProcessingDays: 4,\n        totalUsers: 150\n    },\n    department_head: {\n        totalRequests: 15,\n        pendingReview: 2,\n        approvedToday: 1,\n        rejectedToday: 0,\n        averageProcessingDays: 5,\n        totalUsers: 150\n    },\n    admin_manager: {\n        totalRequests: 120,\n        pendingReview: 8,\n        approvedToday: 5,\n        rejectedToday: 2,\n        averageProcessingDays: 12,\n        totalUsers: 150\n    },\n    minister: {\n        totalRequests: 8,\n        pendingReview: 1,\n        approvedToday: 0,\n        rejectedToday: 0,\n        averageProcessingDays: 7,\n        totalUsers: 150\n    },\n    zakat_applicant: {\n        totalRequests: 3,\n        pendingReview: 1,\n        approvedToday: 0,\n        rejectedToday: 0,\n        averageProcessingDays: 14,\n        totalUsers: 1\n    },\n    system_admin: {\n        totalRequests: 200,\n        pendingReview: 25,\n        approvedToday: 12,\n        rejectedToday: 3,\n        averageProcessingDays: 10,\n        totalUsers: 150\n    }\n};\n// Helper function to get user by ID\nconst getUserById = (id)=>{\n    return mockUsers.find((user)=>user.id === id);\n};\n// Helper function to get user by email\nconst getUserByEmail = (email)=>{\n    return mockUsers.find((user)=>user.email === email);\n};\n// Helper function to get requests by user ID\nconst getRequestsByUserId = (userId)=>{\n    return mockAssistanceRequests.filter((request)=>request.userId === userId);\n};\n// Helper function to get tasks by user ID\nconst getTasksByUserId = (userId)=>{\n    const user = getUserById(userId);\n    if (!user) return [];\n    return mockTasks[user.role] || [];\n};\n// Zakat Category Labels\nconst zakatCategoryLabels = {\n    fuqara: {\n        ar: \"الفقراء\",\n        en: \"The Poor\"\n    },\n    masakin: {\n        ar: \"المساكين\",\n        en: \"The Needy\"\n    },\n    amilin: {\n        ar: \"العاملين عليها\",\n        en: \"Zakat Administrators\"\n    },\n    muallafah: {\n        ar: \"المؤلفة قلوبهم\",\n        en: \"Those whose hearts are reconciled\"\n    },\n    riqab: {\n        ar: \"في الرقاب\",\n        en: \"To free slaves/captives\"\n    },\n    gharimin: {\n        ar: \"الغارمين\",\n        en: \"Those in debt\"\n    },\n    fisabilillah: {\n        ar: \"في سبيل الله\",\n        en: \"In the cause of Allah\"\n    },\n    ibnus_sabil: {\n        ar: \"ابن السبيل\",\n        en: \"The wayfarer/traveler\"\n    }\n};\n// Mock Beneficiaries Data\nconst mockBeneficiaries = [\n    {\n        id: \"ben-001\",\n        fullNameAr: \"أحمد محمد العبدالله\",\n        fullNameEn: \"Ahmed Mohammed Al-Abdullah\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1985-03-15\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966501234567\",\n        email: \"<EMAIL>\",\n        address: \"حي النهضة، شارع الملك فهد\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        postalCode: \"12345\",\n        zakatCategories: [\n            \"fuqara\",\n            \"gharimin\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 85,\n        monthlyIncome: 2500,\n        familySize: 5,\n        dependents: 3,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-15\"),\n        lastVerificationDate: new Date(\"2024-01-20\"),\n        nextReviewDate: new Date(\"2024-07-15\"),\n        caseId: \"case-001\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 15000,\n        lastDistributionDate: new Date(\"2024-01-25\"),\n        distributionCount: 3,\n        documents: [\n            {\n                id: \"doc-001\",\n                type: \"national_id\",\n                name: \"National ID Copy\",\n                uploadDate: new Date(\"2024-01-15\"),\n                verified: true,\n                verifiedBy: \"4\",\n                verifiedAt: new Date(\"2024-01-16\"),\n                fileSize: 1024000,\n                mimeType: \"image/jpeg\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-01-15\"),\n        updatedAt: new Date(\"2024-01-25\"),\n        notes: \"Family breadwinner, needs regular support\"\n    },\n    {\n        id: \"ben-002\",\n        fullNameAr: \"فاطمة علي الزهراء\",\n        fullNameEn: \"Fatima Ali Al-Zahra\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1990-07-22\"),\n        gender: \"female\",\n        maritalStatus: \"widowed\",\n        phoneNumber: \"+966502345678\",\n        email: \"<EMAIL>\",\n        address: \"حي الملز، شارع العليا\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        zakatCategories: [\n            \"masakin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"masakin\",\n        eligibilityScore: 92,\n        monthlyIncome: 1200,\n        familySize: 4,\n        dependents: 3,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-02-01\"),\n        lastVerificationDate: new Date(\"2024-02-05\"),\n        nextReviewDate: new Date(\"2024-08-01\"),\n        caseId: \"case-002\",\n        assignedStaffId: \"5\",\n        priority: \"high\",\n        totalReceived: 22000,\n        lastDistributionDate: new Date(\"2024-02-10\"),\n        distributionCount: 4,\n        familyMembers: [\n            {\n                id: \"fam-001\",\n                name: \"محمد علي الزهراء\",\n                relationship: \"son\",\n                age: 12,\n                isDependent: true,\n                hasSpecialNeeds: false\n            },\n            {\n                id: \"fam-002\",\n                name: \"عائشة علي الزهراء\",\n                relationship: \"daughter\",\n                age: 8,\n                isDependent: true,\n                hasSpecialNeeds: true\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-002\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-02-01\"),\n                verified: true,\n                verifiedBy: \"5\",\n                verifiedAt: new Date(\"2024-02-02\"),\n                fileSize: 2048000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"5\",\n        createdAt: new Date(\"2024-02-01\"),\n        updatedAt: new Date(\"2024-02-10\"),\n        notes: \"Widow with special needs child, priority case\"\n    },\n    {\n        id: \"ben-003\",\n        fullNameAr: \"خالد سعد الغامدي\",\n        fullNameEn: \"Khalid Saad Al-Ghamdi\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1978-11-10\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966503456789\",\n        address: \"حي الشفا، طريق الملك عبدالعزيز\",\n        city: \"جدة\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"gharimin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"gharimin\",\n        eligibilityScore: 78,\n        monthlyIncome: 3200,\n        familySize: 6,\n        dependents: 4,\n        status: \"under_review\",\n        verificationStatus: \"in_progress\",\n        registrationDate: new Date(\"2024-02-15\"),\n        caseId: \"case-003\",\n        assignedStaffId: \"6\",\n        priority: \"medium\",\n        totalReceived: 8000,\n        lastDistributionDate: new Date(\"2024-01-10\"),\n        distributionCount: 2,\n        documents: [\n            {\n                id: \"doc-003\",\n                type: \"income_certificate\",\n                name: \"Income Certificate\",\n                uploadDate: new Date(\"2024-02-15\"),\n                verified: false,\n                fileSize: 1536000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"6\",\n        createdAt: new Date(\"2024-02-15\"),\n        updatedAt: new Date(\"2024-02-16\"),\n        notes: \"Business owner facing financial difficulties\"\n    },\n    {\n        id: \"ben-004\",\n        fullNameAr: \"مريم عبدالرحمن القحطاني\",\n        fullNameEn: \"Maryam Abdulrahman Al-Qahtani\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1995-04-18\"),\n        gender: \"female\",\n        maritalStatus: \"single\",\n        phoneNumber: \"+966504567890\",\n        address: \"حي الروضة، شارع التحلية\",\n        city: \"الدمام\",\n        region: \"المنطقة الشرقية\",\n        zakatCategories: [\n            \"ibnus_sabil\"\n        ],\n        primaryCategory: \"ibnus_sabil\",\n        eligibilityScore: 65,\n        monthlyIncome: 0,\n        familySize: 1,\n        dependents: 0,\n        status: \"pending_verification\",\n        verificationStatus: \"pending\",\n        registrationDate: new Date(\"2024-02-20\"),\n        priority: \"low\",\n        totalReceived: 0,\n        distributionCount: 0,\n        documents: [\n            {\n                id: \"doc-004\",\n                type: \"national_id\",\n                name: \"National ID Copy\",\n                uploadDate: new Date(\"2024-02-20\"),\n                verified: false,\n                fileSize: 896000,\n                mimeType: \"image/png\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-02-20\"),\n        updatedAt: new Date(\"2024-02-20\"),\n        notes: \"Student seeking temporary assistance\"\n    },\n    {\n        id: \"ben-005\",\n        fullNameAr: \"عبدالله يوسف الشهري\",\n        fullNameEn: \"Abdullah Yusuf Al-Shehri\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1982-09-05\"),\n        gender: \"male\",\n        maritalStatus: \"divorced\",\n        phoneNumber: \"+966505678901\",\n        address: \"حي العزيزية، شارع الأمير سلطان\",\n        city: \"الطائف\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"fuqara\",\n            \"gharimin\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 88,\n        monthlyIncome: 1800,\n        familySize: 3,\n        dependents: 2,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-05\"),\n        lastVerificationDate: new Date(\"2024-01-10\"),\n        nextReviewDate: new Date(\"2024-07-05\"),\n        caseId: \"case-005\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 18500,\n        lastDistributionDate: new Date(\"2024-02-01\"),\n        distributionCount: 5,\n        familyMembers: [\n            {\n                id: \"fam-003\",\n                name: \"سارة عبدالله الشهري\",\n                relationship: \"daughter\",\n                age: 10,\n                isDependent: true,\n                hasSpecialNeeds: false\n            },\n            {\n                id: \"fam-004\",\n                name: \"عمر عبدالله الشهري\",\n                relationship: \"son\",\n                age: 7,\n                isDependent: true,\n                hasSpecialNeeds: false\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-005\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-01-05\"),\n                verified: true,\n                verifiedBy: \"4\",\n                verifiedAt: new Date(\"2024-01-06\"),\n                fileSize: 1792000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-01-05\"),\n        updatedAt: new Date(\"2024-02-01\"),\n        notes: \"Divorced father with custody of children\"\n    },\n    {\n        id: \"ben-006\",\n        fullNameAr: \"نورا أحمد الحربي\",\n        fullNameEn: \"Nora Ahmed Al-Harbi\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1988-12-03\"),\n        gender: \"female\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966506789012\",\n        email: \"<EMAIL>\",\n        address: \"حي الملقا، شارع الأمير محمد بن عبدالعزيز\",\n        city: \"الرياض\",\n        region: \"منطقة الرياض\",\n        zakatCategories: [\n            \"masakin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"masakin\",\n        eligibilityScore: 91,\n        monthlyIncome: 1500,\n        familySize: 7,\n        dependents: 5,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2024-01-20\"),\n        lastVerificationDate: new Date(\"2024-01-25\"),\n        nextReviewDate: new Date(\"2024-07-20\"),\n        caseId: \"case-006\",\n        assignedStaffId: \"5\",\n        priority: \"high\",\n        totalReceived: 25000,\n        lastDistributionDate: new Date(\"2024-02-15\"),\n        distributionCount: 6,\n        documents: [\n            {\n                id: \"doc-006\",\n                type: \"family_card\",\n                name: \"Family Registration Card\",\n                uploadDate: new Date(\"2024-01-20\"),\n                verified: true,\n                verifiedBy: \"5\",\n                verifiedAt: new Date(\"2024-01-21\"),\n                fileSize: 2304000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"5\",\n        createdAt: new Date(\"2024-01-20\"),\n        updatedAt: new Date(\"2024-02-15\"),\n        notes: \"Large family with multiple dependents\"\n    },\n    {\n        id: \"ben-007\",\n        fullNameAr: \"محمد عبدالله الدوسري\",\n        fullNameEn: \"Mohammed Abdullah Al-Dosari\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1975-06-14\"),\n        gender: \"male\",\n        maritalStatus: \"married\",\n        phoneNumber: \"+966507890123\",\n        address: \"حي الفيصلية، طريق الملك فهد\",\n        city: \"الدمام\",\n        region: \"المنطقة الشرقية\",\n        zakatCategories: [\n            \"gharimin\",\n            \"fuqara\"\n        ],\n        primaryCategory: \"gharimin\",\n        eligibilityScore: 82,\n        monthlyIncome: 2800,\n        familySize: 4,\n        dependents: 2,\n        status: \"approved\",\n        verificationStatus: \"completed\",\n        registrationDate: new Date(\"2023-12-10\"),\n        lastVerificationDate: new Date(\"2023-12-15\"),\n        nextReviewDate: new Date(\"2024-06-10\"),\n        caseId: \"case-007\",\n        assignedStaffId: \"6\",\n        priority: \"medium\",\n        totalReceived: 12000,\n        lastDistributionDate: new Date(\"2024-01-30\"),\n        distributionCount: 4,\n        documents: [\n            {\n                id: \"doc-007\",\n                type: \"income_certificate\",\n                name: \"Income Certificate\",\n                uploadDate: new Date(\"2023-12-10\"),\n                verified: true,\n                verifiedBy: \"6\",\n                verifiedAt: new Date(\"2023-12-11\"),\n                fileSize: 1280000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"6\",\n        createdAt: new Date(\"2023-12-10\"),\n        updatedAt: new Date(\"2024-01-30\"),\n        notes: \"Small business owner with debt issues\"\n    },\n    {\n        id: \"ben-008\",\n        fullNameAr: \"عائشة سالم القرني\",\n        fullNameEn: \"Aisha Salem Al-Qarni\",\n        nationalId: \"**********\",\n        dateOfBirth: new Date(\"1992-08-27\"),\n        gender: \"female\",\n        maritalStatus: \"single\",\n        phoneNumber: \"+966508901234\",\n        address: \"حي الشرفية، شارع الستين\",\n        city: \"جدة\",\n        region: \"منطقة مكة المكرمة\",\n        zakatCategories: [\n            \"fuqara\"\n        ],\n        primaryCategory: \"fuqara\",\n        eligibilityScore: 75,\n        monthlyIncome: 1000,\n        familySize: 2,\n        dependents: 1,\n        status: \"under_review\",\n        verificationStatus: \"in_progress\",\n        registrationDate: new Date(\"2024-02-25\"),\n        caseId: \"case-008\",\n        assignedStaffId: \"4\",\n        priority: \"medium\",\n        totalReceived: 3000,\n        lastDistributionDate: new Date(\"2024-01-15\"),\n        distributionCount: 1,\n        familyMembers: [\n            {\n                id: \"fam-005\",\n                name: \"فاطمة سالم القرني\",\n                relationship: \"mother\",\n                age: 65,\n                isDependent: true,\n                hasSpecialNeeds: true\n            }\n        ],\n        documents: [\n            {\n                id: \"doc-008\",\n                type: \"medical_report\",\n                name: \"Medical Report for Mother\",\n                uploadDate: new Date(\"2024-02-25\"),\n                verified: false,\n                fileSize: 3072000,\n                mimeType: \"application/pdf\"\n            }\n        ],\n        createdBy: \"4\",\n        createdAt: new Date(\"2024-02-25\"),\n        updatedAt: new Date(\"2024-02-26\"),\n        notes: \"Caring for elderly mother with medical needs\"\n    }\n];\n// Helper functions for beneficiaries\nconst getBeneficiaryById = (id)=>{\n    return mockBeneficiaries.find((beneficiary)=>beneficiary.id === id);\n};\nconst getBeneficiariesByStatus = (status)=>{\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.status === status);\n};\nconst getBeneficiariesByCategory = (category)=>{\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.zakatCategories.includes(category) || beneficiary.primaryCategory === category);\n};\nconst searchBeneficiaries = (searchTerm)=>{\n    const term = searchTerm.toLowerCase();\n    return mockBeneficiaries.filter((beneficiary)=>beneficiary.fullNameAr.toLowerCase().includes(term) || beneficiary.fullNameEn.toLowerCase().includes(term) || beneficiary.nationalId.includes(term) || beneficiary.phoneNumber.includes(term) || beneficiary.email?.toLowerCase().includes(term));\n};\nconst getBeneficiaryStats = ()=>{\n    const total = mockBeneficiaries.length;\n    const approved = mockBeneficiaries.filter((b)=>b.status === \"approved\").length;\n    const pending = mockBeneficiaries.filter((b)=>b.status === \"pending_verification\").length;\n    const underReview = mockBeneficiaries.filter((b)=>b.status === \"under_review\").length;\n    const totalDistributed = mockBeneficiaries.reduce((sum, b)=>sum + b.totalReceived, 0);\n    return {\n        total,\n        approved,\n        pending,\n        underReview,\n        totalDistributed,\n        averageDistribution: total > 0 ? Math.round(totalDistributed / total) : 0\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/mock-data.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDuration(seconds) {\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor(seconds % 3600 / 60);\n    const remainingSeconds = seconds % 60;\n    return `${hours.toString().padStart(2, \"0\")}:${minutes.toString().padStart(2, \"0\")}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QjtBQUVPLFNBQVNDLGVBQWVDLE9BQWU7SUFDNUMsTUFBTUMsUUFBUUMsS0FBS0MsS0FBSyxDQUFDSCxVQUFVO0lBQ25DLE1BQU1JLFVBQVVGLEtBQUtDLEtBQUssQ0FBQyxVQUFXLE9BQVE7SUFDOUMsTUFBTUUsbUJBQW1CTCxVQUFVO0lBRW5DLE9BQU8sQ0FBQyxFQUFFQyxNQUFNSyxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHLEtBQUssQ0FBQyxFQUFFSCxRQUFRRSxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHLEtBQUssQ0FBQyxFQUFFRixpQkFBaUJDLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDO0FBQ3RJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG4gXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0RHVyYXRpb24oc2Vjb25kczogbnVtYmVyKTogc3RyaW5nIHtcbiAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKHNlY29uZHMgLyAzNjAwKVxuICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcigoc2Vjb25kcyAlIDM2MDApIC8gNjApXG4gIGNvbnN0IHJlbWFpbmluZ1NlY29uZHMgPSBzZWNvbmRzICUgNjBcblxuICByZXR1cm4gYCR7aG91cnMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke21pbnV0ZXMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke3JlbWFpbmluZ1NlY29uZHMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWBcbn0iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyIsImZvcm1hdER1cmF0aW9uIiwic2Vjb25kcyIsImhvdXJzIiwiTWF0aCIsImZsb29yIiwibWludXRlcyIsInJlbWFpbmluZ1NlY29uZHMiLCJ0b1N0cmluZyIsInBhZFN0YXJ0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ I18nProvider auto */ \n\n\nfunction I18nProvider({ children }) {\n    // Always render the I18nextProvider to avoid hydration mismatch\n    // The i18n instance is already initialized with resources in lib/i18n.ts\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_i18next__WEBPACK_IMPORTED_MODULE_1__.I18nextProvider, {\n        i18n: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\i18n-provider.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvaTE4bi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBSStDO0FBQ2xCO0FBTXRCLFNBQVNFLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxnRUFBZ0U7SUFDaEUseUVBQXlFO0lBQ3pFLHFCQUNFLDhEQUFDSCwwREFBZUE7UUFBQ0MsTUFBTUEsaURBQUlBO2tCQUN4QkU7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL2kxOG4tcHJvdmlkZXIudHN4PzVkYjIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICdyZWFjdC1pMThuZXh0J1xuaW1wb3J0IGkxOG4gZnJvbSAnQC9saWIvaTE4bidcblxuaW50ZXJmYWNlIEkxOG5Qcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gSTE4blByb3ZpZGVyKHsgY2hpbGRyZW4gfTogSTE4blByb3ZpZGVyUHJvcHMpIHtcbiAgLy8gQWx3YXlzIHJlbmRlciB0aGUgSTE4bmV4dFByb3ZpZGVyIHRvIGF2b2lkIGh5ZHJhdGlvbiBtaXNtYXRjaFxuICAvLyBUaGUgaTE4biBpbnN0YW5jZSBpcyBhbHJlYWR5IGluaXRpYWxpemVkIHdpdGggcmVzb3VyY2VzIGluIGxpYi9pMThuLnRzXG4gIHJldHVybiAoXG4gICAgPEkxOG5leHRQcm92aWRlciBpMThuPXtpMThufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0kxOG5leHRQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkkxOG5leHRQcm92aWRlciIsImkxOG4iLCJJMThuUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/i18n-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SessionProvider auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\providers\\\\session-provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvc2Vzc2lvbi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRzRFO0FBT3JFLFNBQVNBLGdCQUFnQixFQUFFRSxRQUFRLEVBQXdCO0lBQ2hFLHFCQUFPLDhEQUFDRCw0REFBdUJBO2tCQUFFQzs7Ozs7O0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBwLy4vcHJvdmlkZXJzL3Nlc3Npb24tcHJvdmlkZXIudHN4PzIzNWEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4ndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0J1xuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBTZXNzaW9uUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./providers/session-provider.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"42bb61ef2a3f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcHAvLi9hcHAvZ2xvYmFscy5jc3M/YjcwMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQyYmI2MWVmMmEzZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers_session_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/session-provider */ \"(rsc)/./providers/session-provider.tsx\");\n/* harmony import */ var _providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/i18n-provider */ \"(rsc)/./providers/i18n-provider.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"نظام إدارة الزكاة - Zakat Management System\",\n    description: \"نظام شامل لإدارة طلبات الزكاة والمساعدات\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_session_provider__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_i18n_provider__WEBPACK_IMPORTED_MODULE_3__.I18nProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./providers/i18n-provider.tsx":
/*!*************************************!*\
  !*** ./providers/i18n-provider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I18nProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\i18n-provider.tsx#I18nProvider`);


/***/ }),

/***/ "(rsc)/./providers/session-provider.tsx":
/*!****************************************!*\
  !*** ./providers/session-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Documents\VCode projects\zakat-deepagent\zakat_management_system\app\providers\session-provider.tsx#SessionProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/i18next-browser-languagedetector","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/void-elements","vendor-chunks/@radix-ui","vendor-chunks/@floating-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/tslib","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/react-style-singleton","vendor-chunks/clsx","vendor-chunks/get-nonce","vendor-chunks/framer-motion","vendor-chunks/date-fns","vendor-chunks/use-sync-external-store"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Civahr%5COneDrive%5CDocuments%5CVCode%20projects%5Czakat-deepagent%5Czakat_management_system%5Capp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();