{"name": "@floating-ui/react", "version": "0.26.0", "description": "Floating UI for React", "publishConfig": {"access": "public"}, "main": "./dist/floating-ui.react.umd.js", "module": "./dist/floating-ui.react.esm.js", "unpkg": "./dist/floating-ui.react.umd.min.js", "types": "./src/types.d.ts", "exports": {"./package.json": "./package.json", "./utils/package.json": "./utils/package.json", ".": {"import": {"types": "./src/types.d.ts", "default": "./dist/floating-ui.react.mjs"}, "types": "./src/types.d.ts", "module": "./dist/floating-ui.react.esm.js", "default": "./dist/floating-ui.react.umd.js"}, "./utils": {"import": {"types": "./utils/src/index.d.ts", "default": "./utils/dist/floating-ui.react.utils.mjs"}, "types": "./utils/src/index.d.ts", "module": "./utils/dist/floating-ui.react.utils.esm.js", "default": "./utils/dist/floating-ui.react.utils.umd.js"}}, "sideEffects": false, "files": ["dist/", "utils/dist/", "utils/package.json", "**/*.d.ts", "**/*.d.mts"], "scripts": {"test": "vitest", "build": "NODE_ENV=build rollup -c", "dev": "vite"}, "author": "atomiks", "license": "MIT", "bugs": "https://github.com/floating-ui/floating-ui", "repository": {"type": "git", "url": "https://github.com/floating-ui/floating-ui.git", "directory": "packages/react"}, "homepage": "https://floating-ui.com/docs/react", "keywords": ["tooltip", "popover", "dropdown", "menu", "popup", "positioning", "react", "react-dom"], "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@floating-ui/react-dom": "^2.0.2", "@floating-ui/utils": "^0.1.5", "tabbable": "^6.0.1"}, "devDependencies": {"@babel/preset-react": "^7.16.0", "@radix-ui/react-checkbox": "^1.0.2", "@radix-ui/react-icons": "^1.2.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.1.1", "@testing-library/react-hooks": "^7.0.2", "@testing-library/user-event": "^14.4.3", "@types/react": "^18.2.14", "@vitejs/plugin-react": "^4.0.1", "clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "resize-observer-polyfill": "^1.5.1", "use-isomorphic-layout-effect": "^1.1.1"}}