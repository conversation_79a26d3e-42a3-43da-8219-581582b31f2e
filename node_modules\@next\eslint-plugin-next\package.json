{"name": "@next/eslint-plugin-next", "version": "15.3.0", "description": "ESLint plugin for Next.js.", "main": "dist/index.js", "license": "MIT", "repository": {"url": "vercel/next.js", "directory": "packages/eslint-plugin-next"}, "files": ["dist"], "dependencies": {"fast-glob": "3.3.1"}, "devDependencies": {"eslint": "8.56.0"}, "scripts": {"build": "swc -d dist src", "prepublishOnly": "cd ../../ && turbo run build"}}