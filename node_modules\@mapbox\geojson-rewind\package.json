{"name": "@mapbox/geojson-rewind", "version": "0.5.2", "description": "enforce winding order for geo<PERSON><PERSON>", "main": "index.js", "bin": "geo<PERSON><PERSON>-rewind", "directories": {"test": "test"}, "scripts": {"test": "tape test/rewind.js"}, "files": ["index.js", "geo<PERSON><PERSON>-rewind"], "repository": {"type": "git", "url": "git://github.com/mapbox/geojson-rewind.git"}, "keywords": ["g<PERSON><PERSON><PERSON>", "winding", "order", "rendering", "coordinates"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/mapbox/geojson-rewind/issues"}, "homepage": "https://github.com/mapbox/geoj<PERSON>-rewind", "dependencies": {"get-stream": "^6.0.1", "minimist": "^1.2.6"}, "devDependencies": {"tape": "^5.5.3"}}