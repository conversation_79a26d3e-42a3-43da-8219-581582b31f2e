
'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { 
  LayoutDashboard,
  FileText,
  Users,
  ClipboardList,
  BarChart3,
  Settings,
  User,
  Building,
  Shield,
  MessageSquare,
  Calendar,
  CreditCard
} from 'lucide-react'
import type { UserRole } from '@/lib/types'

interface SidebarItem {
  title: string
  href: string
  icon: any
  roles?: UserRole[]
}

const sidebarItems: SidebarItem[] = [
  {
    title: 'dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'profile',
    href: '/profile',
    icon: User,
    roles: ['zakat_applicant'],
  },
  {
    title: 'requests',
    href: '/requests',
    icon: FileText,
  },
  {
    title: 'Beneficiaries',
    href: '/beneficiaries',
    icon: Users,
    roles: ['reception_staff', 'researcher', 'department_head', 'admin_manager', 'minister', 'system_admin'],
  },
  {
    title: 'tasks',
    href: '/tasks',
    icon: ClipboardList,
    roles: ['reception_staff', 'researcher', 'banking_expert', 'department_head', 'admin_manager', 'minister'],
  },
  {
    title: 'reports',
    href: '/reports',
    icon: BarChart3,
    roles: ['reception_staff', 'researcher', 'banking_expert', 'department_head', 'admin_manager', 'minister', 'system_admin'],
  },
  {
    title: 'User Management',
    href: '/admin/users',
    icon: Users,
    roles: ['system_admin'],
  },
  {
    title: 'Assistance Types',
    href: '/admin/assistance-types',
    icon: CreditCard,
    roles: ['system_admin'],
  },
  {
    title: 'System Settings',
    href: '/admin/settings',
    icon: Settings,
    roles: ['system_admin'],
  },
]

export function Sidebar() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()
  const pathname = usePathname()

  if (!session?.user) {
    return null
  }

  const filteredItems = sidebarItems.filter(item => 
    !item.roles || item.roles.includes(session.user.role)
  )

  return (
    <div className="flex h-screen w-64 flex-col border-r bg-background">
      <div className="flex-1 space-y-2 p-4">
        <div className="space-y-1">
          {filteredItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors',
                pathname === item.href
                  ? 'bg-accent text-accent-foreground'
                  : 'text-muted-foreground'
              )}
            >
              <item.icon className="h-4 w-4" />
              {t(item.title)}
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
