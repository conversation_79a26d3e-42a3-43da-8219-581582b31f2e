
export interface User {
  id: string;
  nationalId: string;
  email: string;
  fullName: string;
  nationality: string;
  dateOfBirth: Date;
  phoneNumber: string;
  address: string;
  accountStatus: 'active' | 'inactive' | 'suspended' | 'pending_approval';
  role: UserRole;
  createdAt: Date;
  lastLogin?: Date;
  profileId?: string;
}

export type UserRole =
  | 'zakat_applicant'
  | 'reception_staff'
  | 'researcher'
  | 'banking_expert'
  | 'department_head'
  | 'admin_manager'
  | 'minister'
  | 'system_admin';

// Zakat Categories (8 Islamic categories)
export type ZakatCategory =
  | 'fuqara'      // الفقراء - The Poor
  | 'masakin'     // المساكين - The Needy
  | 'amilin'      // العاملين عليها - Zakat Administrators
  | 'muallafah'   // المؤلفة قلوبهم - Those whose hearts are to be reconciled
  | 'riqab'       // في الرقاب - To free slaves/captives
  | 'gharimin'    // الغارمين - Those in debt
  | 'fisabilillah' // في سبيل الله - In the cause of Allah
  | 'ibnus_sabil'; // ابن السبيل - The wayfarer/traveler

export type BeneficiaryStatus =
  | 'pending_verification'
  | 'under_review'
  | 'approved'
  | 'rejected'
  | 'suspended'
  | 'inactive';

export type CaseStatus =
  | 'new'
  | 'assigned'
  | 'in_progress'
  | 'pending_approval'
  | 'approved'
  | 'rejected'
  | 'closed';

export type VerificationStatus =
  | 'pending'
  | 'in_progress'
  | 'completed'
  | 'failed';

export interface Beneficiary {
  id: string;
  // Personal Information
  fullNameAr: string;
  fullNameEn: string;
  nationalId: string;
  dateOfBirth: Date;
  gender: 'male' | 'female';
  maritalStatus: 'single' | 'married' | 'divorced' | 'widowed';

  // Contact Information
  phoneNumber: string;
  email?: string;
  address: string;
  city: string;
  region: string;
  postalCode?: string;

  // Zakat Information
  zakatCategories: ZakatCategory[];
  primaryCategory: ZakatCategory;
  eligibilityScore: number; // 0-100
  monthlyIncome?: number;
  familySize: number;
  dependents: number;

  // Status and Verification
  status: BeneficiaryStatus;
  verificationStatus: VerificationStatus;
  registrationDate: Date;
  lastVerificationDate?: Date;
  nextReviewDate?: Date;

  // Case Management
  caseId?: string;
  assignedStaffId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';

  // Distribution History
  totalReceived: number;
  lastDistributionDate?: Date;
  distributionCount: number;

  // Family Dependencies
  familyMembers?: FamilyMember[];

  // Documents
  documents: BeneficiaryDocument[];

  // Audit
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  notes?: string;
}

export interface FamilyMember {
  id: string;
  name: string;
  relationship: string;
  age: number;
  isDependent: boolean;
  hasSpecialNeeds: boolean;
  beneficiaryId?: string; // If they are also a beneficiary
}

export interface BeneficiaryDocument {
  id: string;
  type: 'national_id' | 'income_certificate' | 'family_card' | 'medical_report' | 'other';
  name: string;
  uploadDate: Date;
  verified: boolean;
  verifiedBy?: string;
  verifiedAt?: Date;
  fileSize: number;
  mimeType: string;
}

export interface CaseHistory {
  id: string;
  beneficiaryId: string;
  caseId: string;
  action: string;
  description: string;
  performedBy: string;
  performedAt: Date;
  oldStatus?: CaseStatus;
  newStatus?: CaseStatus;
  notes?: string;
}

export interface DistributionRecord {
  id: string;
  beneficiaryId: string;
  amount: number;
  category: ZakatCategory;
  distributionDate: Date;
  voucherNumber: string;
  distributedBy: string;
  notes?: string;
  status: 'pending' | 'completed' | 'cancelled';
}

export interface PersonalProfile {
  id: string;
  userId: string;
  maritalStatus: 'single' | 'married' | 'divorced' | 'widowed';
  familyMembersCount: number;
  familyMembers: FamilyMember[];
  employmentData: EmploymentData;
  monthlyIncome: number;
  assets: Asset[];
  liabilities: Liability[];
  approvalStatus: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  lastUpdateDate: Date;
  createdBy?: string; // Staff member who created it
}

export interface FamilyMember {
  name: string;
  relationship: string;
  age: number;
  nationalId?: string;
  hasDisability: boolean;
  isDependent: boolean;
}

export interface EmploymentData {
  employmentStatus: 'employed' | 'unemployed' | 'retired' | 'disabled' | 'student';
  employerName?: string;
  jobTitle?: string;
  workSector?: string;
  yearsOfExperience?: number;
}

export interface Asset {
  type: 'property' | 'vehicle' | 'business' | 'investment' | 'other';
  description: string;
  estimatedValue: number;
}

export interface Liability {
  type: 'loan' | 'debt' | 'mortgage' | 'other';
  description: string;
  amount: number;
  monthlyPayment: number;
}

export interface AssistanceRequest {
  id: string;
  userId: string;
  assistanceType: AssistanceType;
  requestedAmount: number;
  approvedAmount?: number;
  description: string;
  status: RequestStatus;
  submissionDate: Date;
  lastUpdateDate: Date;
  attachedDocuments: Document[];
  workflow: WorkflowStep[];
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface AssistanceType {
  id: string;
  nameAr: string;
  nameEn: string;
  descriptionAr: string;
  descriptionEn: string;
  maxAmount: number;
  requiredDocuments: RequiredDocument[];
  eligibilityCriteria: EligibilityCriteria[];
  isActive: boolean;
  category: string;
}

export interface RequiredDocument {
  id: string;
  nameAr: string;
  nameEn: string;
  isRequired: boolean;
  acceptedFormats: string[];
  maxSizeKB: number;
}

export interface EligibilityCriteria {
  field: string;
  condition: 'equals' | 'greater_than' | 'less_than' | 'contains';
  value: string | number;
  nationality?: string;
}

export type RequestStatus = 
  | 'draft'
  | 'submitted'
  | 'reception_review'
  | 'researcher_review'
  | 'banking_expert_review'
  | 'department_head_review'
  | 'admin_manager_review'
  | 'minister_review'
  | 'approved'
  | 'rejected'
  | 'needs_more_info'
  | 'returned';

export interface WorkflowStep {
  id: string;
  requestId: string;
  stage: RequestStatus;
  reviewerId?: string;
  reviewerName: string;
  stageStartDate: Date;
  stageEndDate?: Date;
  notes?: string;
  decision?: 'approve' | 'reject' | 'return' | 'needs_more_info';
  decisionDate?: Date;
  attachments?: Document[];
}

export interface Document {
  id: string;
  requestId: string;
  type: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  uploadDate: Date;
  uploadedBy: string;
  verificationStatus: 'pending' | 'verified' | 'rejected';
}

export interface Task {
  id: string;
  assignedTo: string;
  requestId: string;
  type: 'profile_review' | 'request_review' | 'document_verification';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate?: Date;
  status: 'pending' | 'in_progress' | 'completed';
  createdDate: Date;
  completedDate?: Date;
}

export interface Notification {
  id: string;
  userId: string;
  titleAr: string;
  titleEn: string;
  messageAr: string;
  messageEn: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  createdDate: Date;
  actionUrl?: string;
}

export interface DashboardStats {
  totalRequests: number;
  pendingReview: number;
  approvedToday: number;
  rejectedToday: number;
  averageProcessingDays: number;
  totalUsers: number;
}

export interface RolePermissions {
  role: UserRole;
  permissions: Permission[];
}

export interface Permission {
  resource: string;
  actions: string[];
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resourceType: string;
  resourceId: string;
  details: Record<string, any>;
  timestamp: Date;
  ipAddress: string;
}
