!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("@floating-ui/react-dom"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","@floating-ui/react-dom","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReact={},e.<PERSON>act,e.FloatingUIReactDOM,e.ReactDOM)}(this,(function(e,t,n,r){"use strict";function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=o(t);function i(e){return u.useMemo((()=>e.every((e=>null==e))?null:t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}),e)}function c(e){return s(e)?(e.nodeName||"").toLowerCase():"#document"}function l(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function s(e){return e instanceof Node||e instanceof l(e).Node}function a(e){return e instanceof Element||e instanceof l(e).Element}function f(e){return e instanceof HTMLElement||e instanceof l(e).HTMLElement}function d(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof l(e).ShadowRoot)}function m(e){if("html"===c(e))return e;const t=e.assignedSlot||e.parentNode||d(e)&&e.host||function(e){var t;return null==(t=(s(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}(e);return d(t)?t.host:t}function p(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(r=n.shadowRoot)?void 0:r.activeElement);){var n,r;t=t.shadowRoot.activeElement}return t}function v(e,t){if(!e||!t)return!1;const n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&d(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function g(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function h(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function y(e){return!(0!==e.mozInputSource||!e.isTrusted)||(w()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function b(e){return!w()&&0===e.width&&0===e.height||1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail}function E(){return/apple/i.test(navigator.vendor)}function w(){const e=/android/i;return e.test(g())||e.test(h())}function x(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function R(e){return(null==e?void 0:e.ownerDocument)||document}function I(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function k(e){return"composedPath"in e?e.composedPath()[0]:e.target}const O="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function C(e){return f(e)&&e.matches(O)}function T(e){e.preventDefault(),e.stopPropagation()}const M=Math.floor,S="ArrowUp",P="ArrowDown",L="ArrowLeft",A="ArrowRight";function D(e,t,n){return Math.floor(e/t)!==n}function N(e,t){return t<0||t>=e.current.length}function F(e,t){return K(e,{disabledIndices:t})}function j(e,t){return K(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function K(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:u=1}=void 0===t?{}:t;const i=e.current;let c=n;do{var l,s;c+=r?-u:u}while(c>=0&&c<=i.length-1&&(o?o.includes(c):null==i[c]||(null==(l=i[c])?void 0:l.hasAttribute("disabled"))||"true"===(null==(s=i[c])?void 0:s.getAttribute("aria-disabled"))));return c}function H(e,t){let{event:n,orientation:r,loop:o,cols:u,disabledIndices:i,minIndex:c,maxIndex:l,prevIndex:s,stopEvent:a=!1}=t,f=s;if(n.key===S){if(a&&T(n),-1===s)f=l;else if(f=K(e,{startingIndex:f,amount:u,decrement:!0,disabledIndices:i}),o&&(s-u<c||f<0)){const e=s%u,t=l%u,n=l-(t-e);f=t===e?l:t>e?n:n-u}N(e,f)&&(f=s)}if(n.key===P&&(a&&T(n),-1===s?f=c:(f=K(e,{startingIndex:s,amount:u,disabledIndices:i}),o&&s+u>l&&(f=K(e,{startingIndex:s%u-u,amount:u,disabledIndices:i}))),N(e,f)&&(f=s)),"both"===r){const t=M(s/u);n.key===A&&(a&&T(n),s%u!=u-1?(f=K(e,{startingIndex:s,disabledIndices:i}),o&&D(f,u,t)&&(f=K(e,{startingIndex:s-s%u-1,disabledIndices:i}))):o&&(f=K(e,{startingIndex:s-s%u-1,disabledIndices:i})),D(f,u,t)&&(f=s)),n.key===L&&(a&&T(n),s%u!=0?(f=K(e,{startingIndex:s,disabledIndices:i,decrement:!0}),o&&D(f,u,t)&&(f=K(e,{startingIndex:s+(u-s%u),decrement:!0,disabledIndices:i}))):o&&(f=K(e,{startingIndex:s+(u-s%u),decrement:!0,disabledIndices:i})),D(f,u,t)&&(f=s));const r=M(l/u)===t;N(e,f)&&(f=o&&r?n.key===L?l:K(e,{startingIndex:s-s%u-1,disabledIndices:i}):s)}return f}let q=0;function B(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(q);const u=()=>null==e?void 0:e.focus({preventScroll:n});o?u():q=requestAnimationFrame(u)}var _="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;function W(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}const U=u.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function z(e){let{children:t,elementsRef:n,labelsRef:r}=e;const[o,i]=u.useState((()=>new Map)),c=u.useCallback((e=>{i((t=>new Map(t).set(e,null)))}),[]),l=u.useCallback((e=>{i((t=>{const n=new Map(t);return n.delete(e),n}))}),[]);return _((()=>{const e=new Map(o);Array.from(e.keys()).sort(W).forEach(((t,n)=>{e.set(t,n)})),function(e,t){if(e.size!==t.size)return!1;for(const[n,r]of e.entries())if(r!==t.get(n))return!1;return!0}(o,e)||i(e)}),[o]),u.createElement(U.Provider,{value:u.useMemo((()=>({register:c,unregister:l,map:o,elementsRef:n,labelsRef:r})),[c,l,o,n,r])},t)}function X(e){let{label:t}=void 0===e?{}:e;const[n,r]=u.useState(null),o=u.useRef(null),{register:i,unregister:c,map:l,elementsRef:s,labelsRef:a}=u.useContext(U),f=u.useCallback((e=>{if(o.current=e,null!==n&&(s.current[n]=e,a)){var r;const o=void 0!==t;a.current[n]=o?t:null!=(r=null==e?void 0:e.textContent)?r:null}}),[n,s,a,t]);return _((()=>{const e=o.current;if(e)return i(e),()=>{c(e)}}),[i,c]),_((()=>{const e=o.current?l.get(o.current):null;null!=e&&r(e)}),[l]),u.useMemo((()=>({ref:f,index:null==n?-1:n})),[n,f])}function Y(e,t){return"function"==typeof e?e(t):e?u.cloneElement(e,t):u.createElement("div",t)}const V=u.createContext({activeIndex:0,setActiveIndex:()=>{}}),G=[L,A],Z=[S,P],$=[...G,...Z],Q=u.forwardRef((function(e,t){let{render:n,orientation:r="both",loop:o=!0,cols:i=1,disabledIndices:c,...l}=e;const[s,a]=u.useState(0),f=u.useRef([]),d=n&&"function"!=typeof n?n.props:{},m=u.useMemo((()=>({activeIndex:s,setActiveIndex:a})),[s]),p=i>1;const v={...l,...d,ref:t,"aria-orientation":"both"===r?void 0:r,onKeyDown(e){null==l.onKeyDown||l.onKeyDown(e),null==d.onKeyDown||d.onKeyDown(e),function(e){if(!$.includes(e.key))return;const t=F(f,c),n=j(f,c);let u=s;p&&(u=H(f,{event:e,orientation:r,loop:o,cols:i,disabledIndices:c,minIndex:t,maxIndex:n,prevIndex:s}));const l={horizontal:[A],vertical:[P],both:[A,P]}[r],d={horizontal:[L],vertical:[S],both:[L,S]}[r],m=p?$:{horizontal:G,vertical:Z,both:$}[r];u===s&&[...l,...d].includes(e.key)&&(u=o&&u===n&&l.includes(e.key)?t:o&&u===t&&d.includes(e.key)?n:K(f,{startingIndex:u,decrement:d.includes(e.key),disabledIndices:c})),u===s||N(f,u)||(e.stopPropagation(),m.includes(e.key)&&e.preventDefault(),a(u),queueMicrotask((()=>{B(f.current[u])})))}(e)}};return u.createElement(V.Provider,{value:m},u.createElement(z,{elementsRef:f},Y(n,v)))})),J=u.forwardRef((function(e,t){let{render:n,...r}=e;const o=n&&"function"!=typeof n?n.props:{},{activeIndex:c,setActiveIndex:l}=u.useContext(V),{ref:s,index:a}=X(),f=i([s,t,o.ref]),d=c===a;return Y(n,{...r,...o,ref:f,tabIndex:d?0:-1,"data-active":d?"":void 0,onFocus(e){null==r.onFocus||r.onFocus(e),null==o.onFocus||o.onFocus(e),l(a)}})}));function ee(){return ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ee.apply(this,arguments)}let te=!1,ne=0;const re=()=>"floating-ui-"+ne++;const oe=u["useId".toString()]||function(){const[e,t]=u.useState((()=>te?re():void 0));return _((()=>{null==e&&t(re())}),[]),u.useEffect((()=>{te||(te=!0)}),[]),e},ue=u.forwardRef((function(e,t){let{context:{placement:r,elements:{floating:o},middlewareData:{arrow:i}},width:c=14,height:l=7,tipRadius:s=0,strokeWidth:a=0,staticOffset:f,stroke:d,d:m,style:{transform:p,...v}={},...g}=e;const h=oe();if(!o)return null;a*=2;const y=a/2,b=c/2*(s/-8+1),E=l/2*s/4,[w,x]=r.split("-"),R=n.platform.isRTL(o),I=!!m,k="top"===w||"bottom"===w,O=f&&"end"===x?"bottom":"top";let C=f&&"end"===x?"right":"left";f&&R&&(C="end"===x?"left":"right");const T=null!=(null==i?void 0:i.x)?f||i.x:"",M=null!=(null==i?void 0:i.y)?f||i.y:"",S=m||"M0,0 H"+c+" L"+(c-b)+","+(l-E)+" Q"+c/2+","+l+" "+b+","+(l-E)+" Z",P={top:I?"rotate(180deg)":"",left:I?"rotate(90deg)":"rotate(-90deg)",bottom:I?"":"rotate(180deg)",right:I?"rotate(-90deg)":"rotate(90deg)"}[w];return u.createElement("svg",ee({},g,{"aria-hidden":!0,ref:t,width:I?c:c+a,height:c,viewBox:"0 0 "+c+" "+(l>c?l:c),style:{position:"absolute",pointerEvents:"none",[C]:T,[O]:M,[w]:k||I?"100%":"calc(100% - "+a/2+"px)",transform:""+P+(null!=p?p:""),...v}}),a>0&&u.createElement("path",{clipPath:"url(#"+h+")",fill:"none",stroke:d,strokeWidth:a+(m?0:1),d:S}),u.createElement("path",{stroke:a&&!m?g.fill:"none",d:S}),u.createElement("clipPath",{id:h},u.createElement("rect",{x:-y,y:y*(I?-1:1),width:c+a,height:c})))}));function ie(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((e=>e!==n)))||[])}}}const ce=u.createContext(null),le=u.createContext(null),se=()=>{var e;return(null==(e=u.useContext(ce))?void 0:e.id)||null},ae=()=>u.useContext(le);function fe(e){return"data-floating-ui-"+e}function de(e){const n=t.useRef(e);return _((()=>{n.current=e})),n}const me=fe("safe-polygon");function pe(e,t,n){return n&&!x(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}const ve=u.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:()=>{},setState:()=>{},isInstantPhase:!1}),ge=()=>u.useContext(ve);
/*!
  * tabbable 6.0.1
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  */
var he=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"].join(","),ye="undefined"==typeof Element,be=ye?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Ee=!ye&&Element.prototype.getRootNode?function(e){return e.getRootNode()}:function(e){return e.ownerDocument},we=function e(t,n,r){for(var o=[],u=Array.from(t);u.length;){var i=u.shift();if("SLOT"===i.tagName){var c=i.assignedElements(),l=e(c.length?c:i.children,!0,r);r.flatten?o.push.apply(o,l):o.push({scopeParent:i,candidates:l})}else{be.call(i,he)&&r.filter(i)&&(n||!t.includes(i))&&o.push(i);var s=i.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(i),a=!r.shadowRootFilter||r.shadowRootFilter(i);if(s&&a){var f=e(!0===s?i.children:s.children,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:i,candidates:f})}else u.unshift.apply(u,i.children)}}return o},xe=function(e,t){return e.tabIndex<0&&(t||/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||e.isContentEditable)&&isNaN(parseInt(e.getAttribute("tabindex"),10))?0:e.tabIndex},Re=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},Ie=function(e){return"INPUT"===e.tagName},ke=function(e){return function(e){return Ie(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||Ee(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)},Oe=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},Ce=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=be.call(e,"details>summary:first-of-type")?e.parentElement:e;if(be.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return Oe(e)}else{if("function"==typeof r){for(var u=e;e;){var i=e.parentElement,c=Ee(e);if(i&&!i.shadowRoot&&!0===r(i))return Oe(e);e=e.assignedSlot?e.assignedSlot:i||c===e.ownerDocument?i:c.host}e=u}if(function(e){for(var t,n=Ee(e).host,r=!!(null!==(t=n)&&void 0!==t&&t.ownerDocument.contains(n)||e.ownerDocument.contains(e));!r&&n;){var o;r=!(null===(o=n=Ee(n).host)||void 0===o||!o.ownerDocument.contains(n))}return r}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},Te=function(e,t){return!(t.disabled||function(e){return Ie(e)&&"hidden"===e.type}(t)||Ce(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!be.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},Me=function(e,t){return!(ke(t)||xe(t)<0||!Te(e,t))},Se=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},Pe=function e(t){var n=[],r=[];return t.forEach((function(t,o){var u=!!t.scopeParent,i=u?t.scopeParent:t,c=xe(i,u),l=u?e(t.candidates):i;0===c?u?n.push.apply(n,l):n.push(i):r.push({documentOrder:o,tabIndex:c,item:t,isScope:u,content:l})})),r.sort(Re).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},Le=function(e,t){var n;return n=(t=t||{}).getShadowRoot?we([e],t.includeContainer,{filter:Me.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:Se}):function(e,t,n){var r=Array.prototype.slice.apply(e.querySelectorAll(he));return t&&be.call(e,he)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,Me.bind(null,t)),Pe(n)};function Ae(e,t){let n=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),r=n;for(;r.length;)r=e.filter((e=>{var t;return null==(t=r)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})),n=n.concat(r);return n}let De=new WeakMap,Ne=new WeakSet,Fe={},je=0;const Ke=e=>e&&(e.host||Ke(e.parentNode)),He=(e,t)=>t.map((t=>{if(e.contains(t))return t;const n=Ke(t);return e.contains(n)?n:null})).filter((e=>null!=e));function qe(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=R(e[0]).body;return function(e,t,n,r){const o="data-floating-ui-inert",u=r?"inert":n?"aria-hidden":null,i=He(t,e),c=new Set,l=new Set(i),s=[];Fe[o]||(Fe[o]=new WeakMap);const a=Fe[o];return i.forEach((function e(t){t&&!c.has(t)&&(c.add(t),t.parentNode&&e(t.parentNode))})),function e(t){t&&!l.has(t)&&Array.prototype.forEach.call(t.children,(t=>{if(c.has(t))e(t);else{const e=u?t.getAttribute(u):null,n=null!==e&&"false"!==e,r=(De.get(t)||0)+1,i=(a.get(t)||0)+1;De.set(t,r),a.set(t,i),s.push(t),1===r&&n&&Ne.add(t),1===i&&t.setAttribute(o,""),!n&&u&&t.setAttribute(u,"true")}}))}(t),c.clear(),je++,()=>{s.forEach((e=>{const t=(De.get(e)||0)-1,n=(a.get(e)||0)-1;De.set(e,t),a.set(e,n),t||(!Ne.has(e)&&u&&e.removeAttribute(u),Ne.delete(e)),n||e.removeAttribute(o)})),je--,je||(De=new WeakMap,De=new WeakMap,Ne=new WeakSet,Fe={})}}(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}const Be=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function _e(e,t){const n=Le(e,Be());"prev"===t&&n.reverse();const r=n.indexOf(p(R(e)));return n.slice(r+1)[0]}function We(){return _e(document.body,"next")}function Ue(){return _e(document.body,"prev")}function ze(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!v(n,r)}function Xe(e){Le(e,Be()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function Ye(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}const Ve={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0};function Ge(e){"Tab"===e.key&&(e.target,clearTimeout(undefined))}const Ze=u.forwardRef((function(e,t){const[n,r]=u.useState();_((()=>(E()&&r("button"),document.addEventListener("keydown",Ge),()=>{document.removeEventListener("keydown",Ge)})),[]);const o={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[fe("focus-guard")]:"",style:Ve};return u.createElement("span",ee({},e,o))})),$e=u.createContext(null);function Qe(e){let{id:t,root:n}=void 0===e?{}:e;const[r,o]=u.useState(null),i=oe(),c=Je(),l=u.useMemo((()=>({id:t,root:n,portalContext:c,uniqueId:i})),[t,n,c,i]),s=u.useRef();return _((()=>()=>{null==r||r.remove()}),[r,l]),_((()=>{if(s.current===l)return;s.current=l;const{id:e,root:t,portalContext:n,uniqueId:r}=l,u=e?document.getElementById(e):null,i=fe("portal");if(u){const e=document.createElement("div");e.id=r,e.setAttribute(i,""),u.appendChild(e),o(e)}else{let u=t||(null==n?void 0:n.portalNode);u&&!a(u)&&(u=u.current),u=u||document.body;let c=null;e&&(c=document.createElement("div"),c.id=e,u.appendChild(c));const l=document.createElement("div");l.id=r,l.setAttribute(i,""),u=c||u,u.appendChild(l),o(l)}}),[l]),r}const Je=()=>u.useContext($e),et=u.forwardRef((function(e,t){return u.createElement("button",ee({},e,{type:"button",ref:t,tabIndex:-1,style:Ve}))}));const tt=new Set,nt=u.forwardRef((function(e,t){let{lockScroll:n=!1,...r}=e;const o=oe();return _((()=>{if(!n)return;tt.add(o);const e=/iP(hone|ad|od)|iOS/.test(g()),t=document.body.style,r=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",u=window.innerWidth-document.documentElement.clientWidth,i=t.left?parseFloat(t.left):window.pageXOffset,c=t.top?parseFloat(t.top):window.pageYOffset;if(t.overflow="hidden",u&&(t[r]=u+"px"),e){var l,s;const e=(null==(l=window.visualViewport)?void 0:l.offsetLeft)||0,n=(null==(s=window.visualViewport)?void 0:s.offsetTop)||0;Object.assign(t,{position:"fixed",top:-(c-Math.floor(n))+"px",left:-(i-Math.floor(e))+"px",right:"0"})}return()=>{tt.delete(o),0===tt.size&&(Object.assign(t,{overflow:"",[r]:""}),e&&(Object.assign(t,{position:"",top:"",left:"",right:""}),window.scrollTo(i,c)))}}),[o,n]),u.createElement("div",ee({ref:t},r,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...r.style}}))}));function rt(e){return f(e.target)&&"BUTTON"===e.target.tagName}function ot(e){return C(e)}const ut=u["useInsertionEffect".toString()]||(e=>e());function it(e){const t=u.useRef((()=>{}));return ut((()=>{t.current=e})),u.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function ct(e){return null!=e&&null!=e.clientX}const lt={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},st={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"};function at(e,t,n){const r=new Map;return{..."floating"===n&&{tabIndex:-1},...e,...t.map((e=>e?e[n]:null)).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,o]=t;var u;0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof o&&(null==(u=r.get(n))||u.push(o),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),u=0;u<t;u++)o[u]=arguments[u];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=o})),e):e),{})}}let ft=!1;function dt(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function mt(e,t){return dt(t,e===S||e===P,e===L||e===A)}function pt(e,t,n){return dt(t,e===P,n?e===L:e===A)||"Enter"===e||" "==e||""===e}function vt(e,t,n){return dt(t,n?e===A:e===L,e===S)}const gt=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((e,t)=>(t?"-":"")+e.toLowerCase()));function ht(e,t){return"function"==typeof e?e(t):e}function yt(e,t){void 0===t&&(t={});const{open:n,elements:{floating:r}}=e,{duration:o=250}=t,i=("number"==typeof o?o:o.close)||0,[c,l]=u.useState(!1),[s,a]=u.useState("unmounted"),f=function(e,t){const[n,r]=u.useState(e);return e&&!n&&r(!0),u.useEffect((()=>{if(!e){const e=setTimeout((()=>r(!1)),t);return()=>clearTimeout(e)}}),[e,t]),n}(n,i);return _((()=>{c&&!f&&a("unmounted")}),[c,f]),_((()=>{if(r){if(n){a("initial");const e=requestAnimationFrame((()=>{a("open")}));return()=>{cancelAnimationFrame(e)}}l(!0),a("close")}}),[n,r]),{isMounted:f,status:s}}function bt(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}function Et(e,t){const[n,r]=e;let o=!1;const u=t.length;for(let e=0,i=u-1;e<u;i=e++){const[u,c]=t[e]||[0,0],[l,s]=t[i]||[0,0];c>=r!=s>=r&&n<=(l-u)*(r-c)/(s-c)+u&&(o=!o)}return o}Object.defineProperty(e,"arrow",{enumerable:!0,get:function(){return n.arrow}}),Object.defineProperty(e,"autoPlacement",{enumerable:!0,get:function(){return n.autoPlacement}}),Object.defineProperty(e,"autoUpdate",{enumerable:!0,get:function(){return n.autoUpdate}}),Object.defineProperty(e,"computePosition",{enumerable:!0,get:function(){return n.computePosition}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return n.detectOverflow}}),Object.defineProperty(e,"flip",{enumerable:!0,get:function(){return n.flip}}),Object.defineProperty(e,"getOverflowAncestors",{enumerable:!0,get:function(){return n.getOverflowAncestors}}),Object.defineProperty(e,"hide",{enumerable:!0,get:function(){return n.hide}}),Object.defineProperty(e,"inline",{enumerable:!0,get:function(){return n.inline}}),Object.defineProperty(e,"limitShift",{enumerable:!0,get:function(){return n.limitShift}}),Object.defineProperty(e,"offset",{enumerable:!0,get:function(){return n.offset}}),Object.defineProperty(e,"platform",{enumerable:!0,get:function(){return n.platform}}),Object.defineProperty(e,"shift",{enumerable:!0,get:function(){return n.shift}}),Object.defineProperty(e,"size",{enumerable:!0,get:function(){return n.size}}),e.Composite=Q,e.CompositeItem=J,e.FloatingArrow=ue,e.FloatingDelayGroup=e=>{let{children:t,delay:n,timeoutMs:r=0}=e;const[o,i]=u.useReducer(((e,t)=>({...e,...t})),{delay:n,timeoutMs:r,initialDelay:n,currentId:null,isInstantPhase:!1}),c=u.useRef(null),l=u.useCallback((e=>{i({currentId:e})}),[]);return _((()=>{o.currentId?null===c.current?c.current=o.currentId:i({isInstantPhase:!0}):(i({isInstantPhase:!1}),c.current=null)}),[o.currentId]),u.createElement(ve.Provider,{value:u.useMemo((()=>({...o,setState:i,setCurrentId:l})),[o,i,l])},t)},e.FloatingFocusManager=function(e){const{context:t,children:n,disabled:r=!1,order:o=["content"],guards:i=!0,initialFocus:c=0,returnFocus:l=!0,modal:s=!0,visuallyHiddenDismiss:a=!1,closeOnFocusOut:d=!0}=e,{open:m,refs:g,nodeId:h,onOpenChange:y,events:b,dataRef:E,elements:{domReference:w,floating:x}}=t,I="number"==typeof c&&c<0,O="combobox"===(null==w?void 0:w.getAttribute("role"))&&C(w)&&I,M=!O&&s,S="undefined"==typeof HTMLElement||!("inert"in HTMLElement.prototype)||i,P=de(o),L=de(c),A=de(l),D=ae(),N=Je(),F=u.useRef(null),j=u.useRef(null),K=u.useRef(!1),H=u.useRef(null),q=u.useRef(!1),W=null!=N,U=u.useCallback((function(e){return void 0===e&&(e=x),e?Le(e,Be()):[]}),[x]),z=u.useCallback((e=>{const t=U(e);return P.current.map((e=>w&&"reference"===e?w:x&&"floating"===e?x:t)).filter(Boolean).flat()}),[w,x,P,U]);function X(e){return!r&&a&&M?u.createElement(et,{ref:"start"===e?F:j,onClick:e=>y(!1,e.nativeEvent)},"string"==typeof a?a:"Dismiss"):null}u.useEffect((()=>{if(r||!M)return;function e(e){if("Tab"===e.key){v(x,p(R(x)))&&0===U().length&&!O&&T(e);const t=z(),n=k(e);"reference"===P.current[0]&&n===w&&(T(e),e.shiftKey?B(t[t.length-1]):B(t[1])),"floating"===P.current[1]&&n===x&&e.shiftKey&&(T(e),B(t[0]))}}const t=R(x);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}}),[r,w,x,M,P,g,O,U,z]),u.useEffect((()=>{if(!r&&d)return x&&f(w)?(w.addEventListener("focusout",t),w.addEventListener("pointerdown",e),!M&&x.addEventListener("focusout",t),()=>{w.removeEventListener("focusout",t),w.removeEventListener("pointerdown",e),!M&&x.removeEventListener("focusout",t)}):void 0;function e(){q.current=!0,setTimeout((()=>{q.current=!1}))}function t(e){const t=e.relatedTarget;queueMicrotask((()=>{const n=!(v(w,t)||v(x,t)||v(t,x)||v(null==N?void 0:N.portalNode,t)||null!=t&&t.hasAttribute(fe("focus-guard"))||D&&(Ae(D.nodesRef.current,h).find((e=>{var n,r;return v(null==(n=e.context)?void 0:n.elements.floating,t)||v(null==(r=e.context)?void 0:r.elements.domReference,t)}))||function(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}(D.nodesRef.current,h).find((e=>{var n,r;return(null==(n=e.context)?void 0:n.elements.floating)===t||(null==(r=e.context)?void 0:r.elements.domReference)===t}))));t&&n&&!q.current&&t!==H.current&&(K.current=!0,y(!1,e))}))}}),[r,w,x,M,h,D,N,y,d]),u.useEffect((()=>{var e;if(r)return;const t=Array.from((null==N||null==(e=N.portalNode)?void 0:e.querySelectorAll("["+fe("portal")+"]"))||[]);if(x){const e=[x,...t,F.current,j.current,P.current.includes("reference")||O?w:null].filter((e=>null!=e)),n=s||O?qe(e,S,!S):qe(e);return()=>{n()}}}),[r,w,x,s,P,N,O,S]),_((()=>{if(r||!x)return;const e=p(R(x));queueMicrotask((()=>{const t=z(x),n=L.current,r=("number"==typeof n?t[n]:n.current)||x,o=v(x,e);I||o||!m||B(r,{preventScroll:r===x})}))}),[r,m,x,I,z,L]),_((()=>{if(r||!x)return;let e=!1;const t=R(x),n=p(t),o=E.current;function u(t){if("escapeKey"===t.type&&g.domReference.current&&(H.current=g.domReference.current),["referencePress","escapeKey"].includes(t.type))return;const n=t.data.returnFocus;"object"==typeof n?(K.current=!1,e=n.preventScroll):K.current=!n}return H.current=n,b.on("dismiss",u),()=>{b.off("dismiss",u);const n=p(t);(v(x,n)||D&&Ae(D.nodesRef.current,h).some((e=>{var t;return v(null==(t=e.context)?void 0:t.elements.floating,n)}))||o.openEvent&&["click","mousedown"].includes(o.openEvent.type))&&g.domReference.current&&(H.current=g.domReference.current),A.current&&f(H.current)&&!K.current&&B(H.current,{cancelPrevious:!1,preventScroll:e})}}),[r,x,A,E,g,b,D,h]),_((()=>{if(!r&&N)return N.setFocusManagerState({modal:M,closeOnFocusOut:d,open:m,onOpenChange:y,refs:g}),()=>{N.setFocusManagerState(null)}}),[r,N,M,m,y,g,d]),_((()=>{if(r||!x||"function"!=typeof MutationObserver||I)return;const e=()=>{const e=x.getAttribute("tabindex");P.current.includes("floating")||p(R(x))!==g.domReference.current&&0===U().length?"0"!==e&&x.setAttribute("tabindex","0"):"-1"!==e&&x.setAttribute("tabindex","-1")};e();const t=new MutationObserver(e);return t.observe(x,{childList:!0,subtree:!0,attributes:!0}),()=>{t.disconnect()}}),[r,x,g,P,U,I]);const Y=!r&&S&&(W||M);return u.createElement(u.Fragment,null,Y&&u.createElement(Ze,{"data-type":"inside",ref:null==N?void 0:N.beforeInsideRef,onFocus:e=>{if(M){const e=z();B("reference"===o[0]?e[0]:e[e.length-1])}else if(null!=N&&N.preserveTabOrder&&N.portalNode)if(K.current=!1,ze(e,N.portalNode)){const e=We()||w;null==e||e.focus()}else{var t;null==(t=N.beforeOutsideRef.current)||t.focus()}}}),!O&&X("start"),n,X("end"),Y&&u.createElement(Ze,{"data-type":"inside",ref:null==N?void 0:N.afterInsideRef,onFocus:e=>{if(M)B(z()[0]);else if(null!=N&&N.preserveTabOrder&&N.portalNode)if(d&&(K.current=!0),ze(e,N.portalNode)){const e=Ue()||w;null==e||e.focus()}else{var t;null==(t=N.afterOutsideRef.current)||t.focus()}}}))},e.FloatingList=z,e.FloatingNode=function(e){let{children:t,id:n}=e;const r=se();return u.createElement(ce.Provider,{value:u.useMemo((()=>({id:n,parentId:r})),[n,r])},t)},e.FloatingOverlay=nt,e.FloatingPortal=function(e){let{children:t,id:n,root:o=null,preserveTabOrder:i=!0}=e;const c=Qe({id:n,root:o}),[l,s]=u.useState(null),a=u.useRef(null),f=u.useRef(null),d=u.useRef(null),m=u.useRef(null),p=!!l&&!l.modal&&l.open&&i&&!(!o&&!c);return u.useEffect((()=>{if(c&&i&&(null==l||!l.modal))return c.addEventListener("focusin",e,!0),c.addEventListener("focusout",e,!0),()=>{c.removeEventListener("focusin",e,!0),c.removeEventListener("focusout",e,!0)};function e(e){if(c&&ze(e)){("focusin"===e.type?Ye:Xe)(c)}}}),[c,i,null==l?void 0:l.modal]),u.createElement($e.Provider,{value:u.useMemo((()=>({preserveTabOrder:i,beforeOutsideRef:a,afterOutsideRef:f,beforeInsideRef:d,afterInsideRef:m,portalNode:c,setFocusManagerState:s})),[i,c])},p&&c&&u.createElement(Ze,{"data-type":"outside",ref:a,onFocus:e=>{if(ze(e,c)){var t;null==(t=d.current)||t.focus()}else{const e=Ue()||(null==l?void 0:l.refs.domReference.current);null==e||e.focus()}}}),p&&c&&u.createElement("span",{"aria-owns":c.id,style:Ve}),c&&r.createPortal(t,c),p&&c&&u.createElement(Ze,{"data-type":"outside",ref:f,onFocus:e=>{if(ze(e,c)){var t;null==(t=m.current)||t.focus()}else{const t=We()||(null==l?void 0:l.refs.domReference.current);null==t||t.focus(),(null==l?void 0:l.closeOnFocusOut)&&(null==l||l.onOpenChange(!1,e.nativeEvent))}}}))},e.FloatingTree=function(e){let{children:t}=e;const n=u.useRef([]),r=u.useCallback((e=>{n.current=[...n.current,e]}),[]),o=u.useCallback((e=>{n.current=n.current.filter((t=>t!==e))}),[]),i=u.useState((()=>ie()))[0];return u.createElement(le.Provider,{value:u.useMemo((()=>({nodesRef:n,addNode:r,removeNode:o,events:i})),[n,r,o,i])},t)},e.inner=e=>({name:"inner",options:e,async fn(t){const{listRef:o,overflowRef:u,onFallbackChange:i,offset:c=0,index:l=0,minItemsVisible:s=4,referenceOverflowThreshold:a=0,scrollRef:f,...d}=e,{rects:m,elements:{floating:p}}=t,v=o.current[l];if(!v)return{};const g={...t,...await n.offset(-v.offsetTop-p.clientTop-m.reference.height/2-v.offsetHeight/2-c).fn(t)},h=(null==f?void 0:f.current)||p,y=await n.detectOverflow(bt(g,h.scrollHeight),d),b=await n.detectOverflow(g,{...d,elementContext:"reference"}),E=Math.max(0,y.top),w=g.y+E,x=Math.max(0,h.scrollHeight-E-Math.max(0,y.bottom));return h.style.maxHeight=x+"px",h.scrollTop=E,i&&(h.offsetHeight<v.offsetHeight*Math.min(s,o.current.length-1)-1||b.top>=-a||b.bottom>=-a?r.flushSync((()=>i(!0))):r.flushSync((()=>i(!1)))),u&&(u.current=await n.detectOverflow(bt({...g,y:w},h.offsetHeight),d)),{y:w}}}),e.safePolygon=function(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let o,u=!1,i=null,c=null,l=performance.now();const s=e=>{let{x:n,y:s,placement:f,elements:d,onClose:m,nodeId:p,tree:g}=e;return function(e){function h(){clearTimeout(o),m()}if(clearTimeout(o),!d.domReference||!d.floating||null==f||null==n||null==s)return;const{clientX:y,clientY:b}=e,E=[y,b],w=k(e),x="mouseleave"===e.type,R=v(d.floating,w),I=v(d.domReference,w),O=d.domReference.getBoundingClientRect(),C=d.floating.getBoundingClientRect(),T=f.split("-")[0],M=n>C.right-C.width/2,S=s>C.bottom-C.height/2,P=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(E,O),L=C.width>O.width,A=C.height>O.height,D=(L?O:C).left,N=(L?O:C).right,F=(A?O:C).top,j=(A?O:C).bottom;if(R&&(u=!0,!x))return;if(I&&(u=!1),I&&!x)return void(u=!0);if(x&&a(e.relatedTarget)&&v(d.floating,e.relatedTarget))return;if(g&&Ae(g.nodesRef.current,p).some((e=>{let{context:t}=e;return null==t?void 0:t.open})))return;if("top"===T&&s>=O.bottom-1||"bottom"===T&&s<=O.top+1||"left"===T&&n>=O.right-1||"right"===T&&n<=O.left+1)return h();let K=[];switch(T){case"top":K=[[D,O.top+1],[D,C.bottom-1],[N,C.bottom-1],[N,O.top+1]];break;case"bottom":K=[[D,C.top+1],[D,O.bottom-1],[N,O.bottom-1],[N,C.top+1]];break;case"left":K=[[C.right-1,j],[C.right-1,F],[O.left+1,F],[O.left+1,j]];break;case"right":K=[[O.right-1,j],[O.right-1,F],[C.left+1,F],[C.left+1,j]]}if(!Et([y,b],K)){if(u&&!P)return h();if(!x&&r){const t=function(e,t){const n=performance.now(),r=n-l;if(null===i||null===c||0===r)return i=e,c=t,l=n,null;const o=e-i,u=t-c,s=Math.sqrt(o*o+u*u);return i=e,c=t,l=n,s/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return h()}Et([y,b],function(e){let[n,r]=e;switch(T){case"top":return[[L?n+t/2:M?n+4*t:n-4*t,r+t+1],[L?n-t/2:M?n+4*t:n-4*t,r+t+1],...[[C.left,M||L?C.bottom-t:C.top],[C.right,M?L?C.bottom-t:C.top:C.bottom-t]]];case"bottom":return[[L?n+t/2:M?n+4*t:n-4*t,r-t],[L?n-t/2:M?n+4*t:n-4*t,r-t],...[[C.left,M||L?C.top+t:C.bottom],[C.right,M?L?C.top+t:C.bottom:C.top+t]]];case"left":{const e=[n+t+1,A?r+t/2:S?r+4*t:r-4*t],o=[n+t+1,A?r-t/2:S?r+4*t:r-4*t];return[...[[S||A?C.right-t:C.left,C.top],[S?A?C.right-t:C.left:C.right-t,C.bottom]],e,o]}case"right":return[[n-t,A?r+t/2:S?r+4*t:r-4*t],[n-t,A?r-t/2:S?r+4*t:r-4*t],...[[S||A?C.left+t:C.right,C.top],[S?A?C.left+t:C.right:C.left+t,C.bottom]]]}}([n,s]))?!u&&r&&(o=window.setTimeout(h,40)):h()}}};return s.__options={blockPointerEvents:n},s},e.useClick=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,elements:{domReference:i}}=e,{enabled:c=!0,event:l="click",toggle:s=!0,ignoreMouse:a=!1,keyboardHandlers:f=!0}=t,d=u.useRef(),m=u.useRef(!1);return u.useMemo((()=>c?{reference:{onPointerDown(e){d.current=e.pointerType},onMouseDown(e){0===e.button&&(x(d.current,!0)&&a||"click"!==l&&(!n||!s||o.current.openEvent&&"mousedown"!==o.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent)):r(!1,e.nativeEvent)))},onClick(e){"mousedown"===l&&d.current?d.current=void 0:x(d.current,!0)&&a||(!n||!s||o.current.openEvent&&"click"!==o.current.openEvent.type?r(!0,e.nativeEvent):r(!1,e.nativeEvent))},onKeyDown(e){d.current=void 0,e.defaultPrevented||!f||rt(e)||(" "!==e.key||ot(i)||(e.preventDefault(),m.current=!0),"Enter"===e.key&&r(!n||!s,e.nativeEvent))},onKeyUp(e){e.defaultPrevented||!f||rt(e)||ot(i)||" "===e.key&&m.current&&(m.current=!1,r(!n||!s,e.nativeEvent))}}}:{}),[c,o,l,a,f,i,s,n,r])},e.useClientPoint=function(e,t){void 0===t&&(t={});const{open:n,refs:r,dataRef:o,elements:{floating:i}}=e,{enabled:c=!0,axis:s="both",x:a=null,y:f=null}=t,d=u.useRef(!1),m=u.useRef(null),[p,g]=u.useState(),[h,y]=u.useState([]),b=it(((e,t)=>{d.current||o.current.openEvent&&!ct(o.current.openEvent)||r.setPositionReference(function(e,t){let n=null,r=null,o=!1;return{contextElement:e.current||void 0,getBoundingClientRect(){var u,i;const c=(null==(u=e.current)?void 0:u.getBoundingClientRect())||{width:0,height:0,x:0,y:0},l="x"===t.axis||"both"===t.axis,s="y"===t.axis||"both"===t.axis,a=["mouseenter","mousemove"].includes((null==(i=t.dataRef.current.openEvent)?void 0:i.type)||"")&&"touch"!==t.pointerType;let f=c.width,d=c.height,m=c.x,p=c.y;return null==n&&t.x&&l&&(n=c.x-t.x),null==r&&t.y&&s&&(r=c.y-t.y),m-=n||0,p-=r||0,f=0,d=0,!o||a?(f="y"===t.axis?c.width:0,d="x"===t.axis?c.height:0,m=l&&null!=t.x?t.x:m,p=s&&null!=t.y?t.y:p):o&&!a&&(d="x"===t.axis?c.height:d,f="y"===t.axis?c.width:f),o=!0,{width:f,height:d,x:m,y:p,top:p,right:m+f,bottom:p+d,left:m}}}}(r.domReference,{x:e,y:t,axis:s,dataRef:o,pointerType:p}))})),E=it((e=>{null==a&&null==f&&(n?m.current||y([]):b(e.clientX,e.clientY))})),w=x(p)?i:n,R=u.useCallback((()=>{if(!w||!c||null!=a||null!=f)return;const e=l(r.floating.current);function t(n){const o=k(n);v(r.floating.current,o)?(e.removeEventListener("mousemove",t),m.current=null):b(n.clientX,n.clientY)}if(!o.current.openEvent||ct(o.current.openEvent)){e.addEventListener("mousemove",t);const n=()=>{e.removeEventListener("mousemove",t),m.current=null};return m.current=n,n}r.setPositionReference(r.domReference.current)}),[o,c,w,r,b,a,f]);return u.useEffect((()=>R()),[R,h]),u.useEffect((()=>{c&&!i&&(d.current=!1)}),[c,i]),u.useEffect((()=>{!c&&n&&(d.current=!0)}),[c,n]),_((()=>{!c||null==a&&null==f||(d.current=!1,b(a,f))}),[c,a,f,b]),u.useMemo((()=>{if(!c)return{};function e(e){let{pointerType:t}=e;g(t)}return{reference:{onPointerDown:e,onPointerEnter:e,onMouseMove:E,onMouseEnter:E}}}),[c,E])},e.useDelayGroup=(e,t)=>{let{open:n,onOpenChange:r}=e,{id:o}=t;const{currentId:u,setCurrentId:i,initialDelay:c,setState:l,timeoutMs:s}=ge();_((()=>{u&&(l({delay:{open:1,close:pe(c,"close")}}),u!==o&&r(!1))}),[o,r,l,u,c]),_((()=>{function e(){r(!1),l({delay:c,currentId:null})}if(!n&&u===o){if(s){const t=window.setTimeout(e,s);return()=>{clearTimeout(t)}}e()}}),[n,l,u,o,r,c,s]),_((()=>{n&&i(o)}),[n,i,o])},e.useDelayGroupContext=ge,e.useDismiss=function(e,t){void 0===t&&(t={});const{open:r,onOpenChange:o,events:i,nodeId:s,elements:{reference:d,domReference:p,floating:g},dataRef:h}=e,{enabled:E=!0,escapeKey:w=!0,outsidePress:x=!0,outsidePressEvent:O="pointerdown",referencePress:C=!1,referencePressEvent:T="pointerdown",ancestorScroll:M=!1,bubbles:S}=t,P=ae(),L=null!=se(),A=it("function"==typeof x?x:()=>!1),D="function"==typeof x?A:x,N=u.useRef(!1),F=u.useRef(!1),{escapeKeyBubbles:j,outsidePressBubbles:K}=(e=>{var t,n;return{escapeKeyBubbles:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePressBubbles:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}})(S),H=it((e=>{if(!r||!E||!w||"Escape"!==e.key)return;const t=P?Ae(P.nodesRef.current,s):[];if(!j&&(e.stopPropagation(),t.length>0)){let e=!0;if(t.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)})),!e)return}i.emit("dismiss",{type:"escapeKey",data:{returnFocus:{preventScroll:!1}}}),o(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e)})),q=it((e=>{const t=N.current;N.current=!1;const n=F.current;if(F.current=!1,"click"===O&&n)return;if(t)return;if("function"==typeof D&&!D(e))return;const r=k(e),u="["+fe("inert")+"]",d=R(g).querySelectorAll(u);let h=a(r)?r:null;for(;h&&!["html","body","#document"].includes(c(h));){const e=m(h);if(e===R(g).body||!a(e))break;h=e}if(d.length&&a(r)&&!r.matches("html,body")&&!v(r,g)&&Array.from(d).every((e=>!v(h,e))))return;if(f(r)&&g){const t=r.clientWidth>0&&r.scrollWidth>r.clientWidth,n=r.clientHeight>0&&r.scrollHeight>r.clientHeight;let o=n&&e.offsetX>r.clientWidth;if(n){const t="rtl"===function(e){return l(e).getComputedStyle(e)}(r).direction;t&&(o=e.offsetX<=r.offsetWidth-r.clientWidth)}if(o||t&&e.offsetY>r.clientHeight)return}const E=P&&Ae(P.nodesRef.current,s).some((t=>{var n;return I(e,null==(n=t.context)?void 0:n.elements.floating)}));if(I(e,g)||I(e,p)||E)return;const w=P?Ae(P.nodesRef.current,s):[];if(w.length>0){let e=!0;if(w.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)})),!e)return}i.emit("dismiss",{type:"outsidePress",data:{returnFocus:L?{preventScroll:!0}:y(e)||b(e)}}),o(!1,e)}));return u.useEffect((()=>{if(!r||!E)return;function e(e){o(!1,e)}h.current.__escapeKeyBubbles=j,h.current.__outsidePressBubbles=K;const t=R(g);w&&t.addEventListener("keydown",H),D&&t.addEventListener(O,q);let u=[];return M&&(a(p)&&(u=n.getOverflowAncestors(p)),a(g)&&(u=u.concat(n.getOverflowAncestors(g))),!a(d)&&d&&d.contextElement&&(u=u.concat(n.getOverflowAncestors(d.contextElement)))),u=u.filter((e=>{var n;return e!==(null==(n=t.defaultView)?void 0:n.visualViewport)})),u.forEach((t=>{t.addEventListener("scroll",e,{passive:!0})})),()=>{w&&t.removeEventListener("keydown",H),D&&t.removeEventListener(O,q),u.forEach((t=>{t.removeEventListener("scroll",e)}))}}),[h,g,p,d,w,D,O,r,o,M,E,j,K,H,q]),u.useEffect((()=>{N.current=!1}),[D,O]),u.useMemo((()=>E?{reference:{onKeyDown:H,[lt[T]]:e=>{C&&(i.emit("dismiss",{type:"referencePress",data:{returnFocus:!1}}),o(!1,e.nativeEvent))}},floating:{onKeyDown:H,onMouseDown(){F.current=!0},onMouseUp(){F.current=!0},[st[O]]:()=>{N.current=!0}}}:{}),[E,i,C,O,T,o,H])},e.useFloating=function(e){var t;void 0===e&&(e={});const{open:r=!1,onOpenChange:o,nodeId:i}=e,[c,l]=u.useState(null),s=(null==(t=e.elements)?void 0:t.reference)||c,f=n.useFloating(e),d=ae(),m=it(((e,t)=>{e&&(v.current.openEvent=t),null==o||o(e,t)})),p=u.useRef(null),v=u.useRef({}),g=u.useState((()=>ie()))[0],h=oe(),y=u.useCallback((e=>{const t=a(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;f.refs.setReference(t)}),[f.refs]),b=u.useCallback((e=>{(a(e)||null===e)&&(p.current=e,l(e)),(a(f.refs.reference.current)||null===f.refs.reference.current||null!==e&&!a(e))&&f.refs.setReference(e)}),[f.refs]),E=u.useMemo((()=>({...f.refs,setReference:b,setPositionReference:y,domReference:p})),[f.refs,b,y]),w=u.useMemo((()=>({...f.elements,domReference:s})),[f.elements,s]),x=u.useMemo((()=>({...f,refs:E,elements:w,dataRef:v,nodeId:i,floatingId:h,events:g,open:r,onOpenChange:m})),[f,i,h,g,r,m,E,w]);return _((()=>{const e=null==d?void 0:d.nodesRef.current.find((e=>e.id===i));e&&(e.context=x)})),u.useMemo((()=>({...f,context:x,refs:E,elements:w})),[f,E,w,x])},e.useFloatingNodeId=function(e){const t=oe(),n=ae(),r=se(),o=e||r;return _((()=>{const e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}}),[n,t,o]),t},e.useFloatingParentNodeId=se,e.useFloatingPortalNode=Qe,e.useFloatingTree=ae,e.useFocus=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,events:o,refs:i,elements:{floating:c,domReference:s}}=e,{enabled:d=!0,visibleOnly:m=!0}=t,g=u.useRef(!1),h=u.useRef(),y=u.useRef(!1);return u.useEffect((()=>{if(!d)return;const e=l(s);function t(){!n&&f(s)&&s===p(R(s))&&(g.current=!0)}function r(){y.current=!0}return e.addEventListener("blur",t),e.addEventListener("keydown",r,!0),()=>{e.removeEventListener("blur",t),e.removeEventListener("keydown",r,!0)}}),[c,s,n,d]),u.useEffect((()=>{if(d)return o.on("dismiss",e),()=>{o.off("dismiss",e)};function e(e){"referencePress"!==e.type&&"escapeKey"!==e.type||(g.current=!0)}}),[o,d]),u.useEffect((()=>()=>{clearTimeout(h.current)}),[]),u.useMemo((()=>d?{reference:{onPointerDown(){y.current=!1},onMouseLeave(){g.current=!1},onFocus(e){if(g.current)return;const t=k(e.nativeEvent);if(m&&a(t))try{if(E())throw Error();if(!t.matches(":focus-visible"))return}catch(e){if(!y.current&&!C(t))return}r(!0,e.nativeEvent)},onBlur(e){g.current=!1;const t=e.relatedTarget,n=a(t)&&t.hasAttribute(fe("focus-guard"))&&"outside"===t.getAttribute("data-type");h.current=window.setTimeout((()=>{const o=p(s?s.ownerDocument:document);(t||o!==s)&&(v(i.floating.current,t)||v(s,t)||n||r(!1,e.nativeEvent))}))}}}:{}),[d,m,s,i,r])},e.useHover=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,events:i,elements:{domReference:c,floating:l},refs:s}=e,{enabled:f=!0,delay:d=0,handleClose:m=null,mouseOnly:p=!1,restMs:g=0,move:h=!0}=t,y=ae(),b=se(),E=de(m),w=de(d),I=u.useRef(),k=u.useRef(),O=u.useRef(),C=u.useRef(),T=u.useRef(!0),M=u.useRef(!1),S=u.useRef((()=>{})),P=u.useCallback((()=>{var e;const t=null==(e=o.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}),[o]);u.useEffect((()=>{if(f)return i.on("dismiss",e),()=>{i.off("dismiss",e)};function e(){clearTimeout(k.current),clearTimeout(C.current),T.current=!0}}),[f,i]),u.useEffect((()=>{if(!f||!E.current||!n)return;function e(e){P()&&r(!1,e)}const t=R(l).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[l,n,r,f,E,o,P]);const L=u.useCallback((function(e,t){void 0===t&&(t=!0);const n=pe(w.current,"close",I.current);n&&!O.current?(clearTimeout(k.current),k.current=setTimeout((()=>r(!1,e)),n)):t&&(clearTimeout(k.current),r(!1,e))}),[w,r]),A=u.useCallback((()=>{S.current(),O.current=void 0}),[]),D=u.useCallback((()=>{if(M.current){const e=R(s.floating.current).body;e.style.pointerEvents="",e.removeAttribute(me),M.current=!1}}),[s]);return u.useEffect((()=>{if(f&&a(c)){const e=c;return n&&e.addEventListener("mouseleave",s),null==l||l.addEventListener("mouseleave",s),h&&e.addEventListener("mousemove",u,{once:!0}),e.addEventListener("mouseenter",u),e.addEventListener("mouseleave",i),()=>{n&&e.removeEventListener("mouseleave",s),null==l||l.removeEventListener("mouseleave",s),h&&e.removeEventListener("mousemove",u),e.removeEventListener("mouseenter",u),e.removeEventListener("mouseleave",i)}}function t(){return!!o.current.openEvent&&["click","mousedown"].includes(o.current.openEvent.type)}function u(e){if(clearTimeout(k.current),T.current=!1,p&&!x(I.current)||g>0&&0===pe(w.current,"open"))return;const t=pe(w.current,"open",I.current);t?k.current=setTimeout((()=>{r(!0,e)}),t):r(!0,e)}function i(r){if(t())return;S.current();const o=R(l);if(clearTimeout(C.current),E.current){n||clearTimeout(k.current),O.current=E.current({...e,tree:y,x:r.clientX,y:r.clientY,onClose(){D(),A(),L(r)}});const t=O.current;return o.addEventListener("mousemove",t),void(S.current=()=>{o.removeEventListener("mousemove",t)})}("touch"!==I.current||!v(l,r.relatedTarget))&&L(r)}function s(n){t()||null==E.current||E.current({...e,tree:y,x:n.clientX,y:n.clientY,onClose(){D(),A(),L(n)}})(n)}}),[c,l,f,e,p,g,h,L,A,D,r,n,y,w,E,o]),_((()=>{var e;if(f&&n&&null!=(e=E.current)&&e.__options.blockPointerEvents&&P()){const e=R(l).body;if(e.setAttribute(me,""),e.style.pointerEvents="none",M.current=!0,a(c)&&l){var t,r;const e=c,n=null==y||null==(t=y.nodesRef.current.find((e=>e.id===b)))||null==(r=t.context)?void 0:r.elements.floating;return n&&(n.style.pointerEvents=""),e.style.pointerEvents="auto",l.style.pointerEvents="auto",()=>{e.style.pointerEvents="",l.style.pointerEvents=""}}}}),[f,n,b,l,c,y,E,o,P]),_((()=>{n||(I.current=void 0,A(),D())}),[n,A,D]),u.useEffect((()=>()=>{A(),clearTimeout(k.current),clearTimeout(C.current),D()}),[f,c,A,D]),u.useMemo((()=>{if(!f)return{};function e(e){I.current=e.pointerType}return{reference:{onPointerDown:e,onPointerEnter:e,onMouseMove(e){n||0===g||(clearTimeout(C.current),C.current=setTimeout((()=>{T.current||r(!0,e.nativeEvent)}),g))}},floating:{onMouseEnter(){clearTimeout(k.current)},onMouseLeave(e){i.emit("dismiss",{type:"mouseLeave",data:{returnFocus:!1}}),L(e.nativeEvent,!1)}}}}),[i,f,g,n,r,L])},e.useId=oe,e.useInnerOffset=function(e,t){const{open:n,elements:o}=e,{enabled:i=!0,overflowRef:c,scrollRef:l,onChange:s}=t,a=it(s),f=u.useRef(!1),d=u.useRef(null),m=u.useRef(null);return u.useEffect((()=>{if(!i)return;function e(e){if(e.ctrlKey||!t||null==c.current)return;const n=e.deltaY,o=c.current.top>=-.5,u=c.current.bottom>=-.5,i=t.scrollHeight-t.clientHeight,l=n<0?-1:1,s=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!o&&n>0||!u&&n<0?(e.preventDefault(),r.flushSync((()=>{a((e=>e+Math[s](n,i*l)))}))):/firefox/i.test(h())&&(t.scrollTop+=n))}const t=(null==l?void 0:l.current)||o.floating;return n&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{d.current=t.scrollTop,null!=c.current&&(m.current={...c.current})})),()=>{d.current=null,m.current=null,t.removeEventListener("wheel",e)}):void 0}),[i,n,o.floating,c,l,a]),u.useMemo((()=>i?{floating:{onKeyDown(){f.current=!0},onWheel(){f.current=!1},onPointerMove(){f.current=!1},onScroll(){const e=(null==l?void 0:l.current)||o.floating;if(c.current&&e&&f.current){if(null!==d.current){const t=e.scrollTop-d.current;(c.current.bottom<-.5&&t<-1||c.current.top<-.5&&t>1)&&r.flushSync((()=>a((e=>e+t))))}requestAnimationFrame((()=>{d.current=e.scrollTop}))}}}}:{}),[i,c,o.floating,l,a])},e.useInteractions=function(e){void 0===e&&(e=[]);const t=e,n=u.useCallback((t=>at(t,e,"reference")),t),r=u.useCallback((t=>at(t,e,"floating")),t),o=u.useCallback((t=>at(t,e,"item")),e.map((e=>null==e?void 0:e.item)));return u.useMemo((()=>({getReferenceProps:n,getFloatingProps:r,getItemProps:o})),[n,r,o])},e.useListItem=X,e.useListNavigation=function(e,t){const{open:n,onOpenChange:r,refs:o,elements:{domReference:i,floating:c}}=e,{listRef:l,activeIndex:s,onNavigate:a=(()=>{}),enabled:d=!0,selectedIndex:m=null,allowEscape:h=!1,loop:w=!1,nested:x=!1,rtl:I=!1,virtual:k=!1,focusItemOnOpen:O="auto",focusItemOnHover:C=!0,openOnArrowKeyDown:M=!0,disabledIndices:S,orientation:D="vertical",cols:q=1,scrollItemIntoView:W=!0,virtualItemRef:U}=t,z=se(),X=ae(),Y=it(a),V=u.useRef(O),G=u.useRef(null!=m?m:-1),Z=u.useRef(null),$=u.useRef(!0),Q=u.useRef(Y),J=u.useRef(!!c),ee=u.useRef(!1),te=u.useRef(!1),ne=de(S),re=de(n),oe=de(W),[ue,ie]=u.useState(),[ce,le]=u.useState(),fe=it((function(e,t,n){void 0===n&&(n=!1);const r=e.current[t.current];r&&(k?(ie(r.id),null==X||X.events.emit("virtualfocus",r),U&&(U.current=r)):B(r,{preventScroll:!0,sync:!(!g().toLowerCase().startsWith("mac")||navigator.maxTouchPoints||!E())&&(ft||ee.current)}),requestAnimationFrame((()=>{const e=oe.current;e&&r&&(n||!$.current)&&(null==r.scrollIntoView||r.scrollIntoView("boolean"==typeof e?{block:"nearest",inline:"nearest"}:e))})))}));_((()=>{document.createElement("div").focus({get preventScroll(){return ft=!0,!1}})}),[]),_((()=>{d&&(n&&c?V.current&&null!=m&&(te.current=!0,Y(m)):J.current&&(G.current=-1,Q.current(null)))}),[d,n,c,m,Y]),_((()=>{if(d&&n&&c)if(null==s){if(ee.current=!1,null!=m)return;if(J.current&&(G.current=-1,fe(l,G)),!J.current&&V.current&&(null!=Z.current||!0===V.current&&null==Z.current)){let e=0;const t=()=>{if(null==l.current[0]){if(e<2){(e?requestAnimationFrame:queueMicrotask)(t)}e++}else G.current=null==Z.current||pt(Z.current,D,I)||x?F(l,ne.current):j(l,ne.current),Z.current=null,Y(G.current)};t()}}else N(l,s)||(G.current=s,fe(l,G,te.current),te.current=!1)}),[d,n,c,s,m,x,l,D,I,Y,fe,ne]),_((()=>{var e,t;if(!d||c||!X||k||!J.current)return;const n=X.nodesRef.current,r=null==(e=n.find((e=>e.id===z)))||null==(t=e.context)?void 0:t.elements.floating,o=p(R(c)),u=n.some((e=>e.context&&v(e.context.elements.floating,o)));r&&!u&&$.current&&r.focus({preventScroll:!0})}),[d,c,X,z,k]),_((()=>{if(d&&X&&k&&!z)return X.events.on("virtualfocus",e),()=>{X.events.off("virtualfocus",e)};function e(e){le(e.id),U&&(U.current=e)}}),[d,X,k,z,U]),_((()=>{Q.current=Y,J.current=!!c})),_((()=>{n||(Z.current=null)}),[n]);const me=null!=s,pe=u.useMemo((()=>{function e(e){if(!n)return;const t=l.current.indexOf(e);-1!==t&&Y(t)}return{onFocus(t){let{currentTarget:n}=t;e(n)},onClick:e=>{let{currentTarget:t}=e;return t.focus({preventScroll:!0})},...C&&{onMouseMove(t){let{currentTarget:n}=t;e(n)},onPointerLeave(e){let{pointerType:t}=e;$.current&&"touch"!==t&&(G.current=-1,fe(l,G),Y(null),k||B(o.floating.current,{preventScroll:!0}))}}}}),[n,o,fe,C,l,Y,k]);return u.useMemo((()=>{if(!d)return{};const e=ne.current;function t(t){if($.current=!1,ee.current=!0,!re.current&&t.currentTarget===o.floating.current)return;if(x&&vt(t.key,D,I))return T(t),r(!1,t.nativeEvent),void(f(i)&&!k&&i.focus());const u=G.current,c=F(l,e),s=j(l,e);if("Home"===t.key&&(T(t),G.current=c,Y(G.current)),"End"===t.key&&(T(t),G.current=s,Y(G.current)),!(q>1&&(G.current=H(l,{event:t,orientation:D,loop:w,cols:q,disabledIndices:e,minIndex:c,maxIndex:s,prevIndex:G.current,stopEvent:!0}),Y(G.current),"both"===D))&&mt(t.key,D)){if(T(t),n&&!k&&p(t.currentTarget.ownerDocument)===t.currentTarget)return G.current=pt(t.key,D,I)?c:s,void Y(G.current);pt(t.key,D,I)?G.current=w?u>=s?h&&u!==l.current.length?-1:c:K(l,{startingIndex:u,disabledIndices:e}):Math.min(s,K(l,{startingIndex:u,disabledIndices:e})):G.current=w?u<=c?h&&-1!==u?l.current.length:s:K(l,{startingIndex:u,decrement:!0,disabledIndices:e}):Math.max(c,K(l,{startingIndex:u,decrement:!0,disabledIndices:e})),N(l,G.current)?Y(null):Y(G.current)}}function u(e){"auto"===O&&y(e.nativeEvent)&&(V.current=!0)}const c=k&&n&&me&&{"aria-activedescendant":ce||ue},s=l.current.find((e=>(null==e?void 0:e.id)===ue));return{reference:{...c,onKeyDown(o){$.current=!1;const u=0===o.key.indexOf("Arrow"),i=function(e,t,n){return dt(t,n?e===L:e===A,e===P)}(o.key,D,I),c=vt(o.key,D,I),a=mt(o.key,D),f=(x?i:a)||"Enter"===o.key||""===o.key.trim();if(k&&n){const e=null==X?void 0:X.nodesRef.current.find((e=>null==e.parentId)),n=X&&e?function(e,t){let n,r=-1;return function t(o,u){u>r&&(n=o,r=u),Ae(e,o).forEach((e=>{t(e.id,u+1)}))}(t,0),e.find((e=>e.id===n))}(X.nodesRef.current,e.id):null;if(u&&n&&U){const e=new KeyboardEvent("keydown",{key:o.key,bubbles:!0});if(i||c){var d,p;const t=(null==(d=n.context)?void 0:d.elements.domReference)===o.currentTarget,r=c&&!t?null==(p=n.context)?void 0:p.elements.domReference:i?s:null;r&&(T(o),r.dispatchEvent(e),le(void 0))}var v;if(a&&n.context)if(n.context.open&&n.parentId&&o.currentTarget!==n.context.elements.domReference)return T(o),void(null==(v=n.context.elements.domReference)||v.dispatchEvent(e))}return t(o)}(n||M||!u)&&(f&&(Z.current=x&&a?null:o.key),x?i&&(T(o),n?(G.current=F(l,e),Y(G.current)):r(!0,o.nativeEvent)):a&&(null!=m&&(G.current=m),T(o),!n&&M?r(!0,o.nativeEvent):t(o),n&&Y(G.current)))},onFocus(){n&&Y(null)},onPointerDown:function(e){V.current=O,"auto"===O&&b(e.nativeEvent)&&(V.current=!0)},onMouseDown:u,onClick:u},floating:{"aria-orientation":"both"===D?void 0:D,...c,onKeyDown:t,onPointerMove(){$.current=!0}},item:pe}}),[i,o,ue,ce,ne,re,l,d,D,I,k,n,me,x,m,M,h,q,w,O,Y,r,pe,X,U])},e.useMergeRefs=i,e.useRole=function(e,t){void 0===t&&(t={});const{open:n,floatingId:r}=e,{enabled:o=!0,role:i="dialog"}=t,c=oe();return u.useMemo((()=>{const e={id:r,role:i};return o?"tooltip"===i?{reference:{"aria-describedby":n?r:void 0},floating:e}:{reference:{"aria-expanded":n?"true":"false","aria-haspopup":"alertdialog"===i?"dialog":i,"aria-controls":n?r:void 0,..."listbox"===i&&{role:"combobox"},..."menu"===i&&{id:c}},floating:{...e,..."menu"===i&&{"aria-labelledby":c}}}:{}}),[o,i,n,r,c])},e.useTransitionStatus=yt,e.useTransitionStyles=function(e,t){void 0===t&&(t={});const{initial:n={opacity:0},open:r,close:o,common:i,duration:c=250}=t,l=e.placement,s=l.split("-")[0],a=u.useMemo((()=>({side:s,placement:l})),[s,l]),f="number"==typeof c,d=(f?c:c.open)||0,m=(f?c:c.close)||0,[p,v]=u.useState((()=>({...ht(i,a),...ht(n,a)}))),{isMounted:g,status:h}=yt(e,{duration:c}),y=de(n),b=de(r),E=de(o),w=de(i);return _((()=>{const e=ht(y.current,a),t=ht(E.current,a),n=ht(w.current,a),r=ht(b.current,a)||Object.keys(e).reduce(((e,t)=>(e[t]="",e)),{});if("initial"===h&&v((t=>({transitionProperty:t.transitionProperty,...n,...e}))),"open"===h&&v({transitionProperty:Object.keys(r).map(gt).join(","),transitionDuration:d+"ms",...n,...r}),"close"===h){const r=t||e;v({transitionProperty:Object.keys(r).map(gt).join(","),transitionDuration:m+"ms",...n,...r})}}),[m,E,y,b,w,d,h,a]),{isMounted:g,styles:p}},e.useTypeahead=function(e,t){var n;const{open:r,dataRef:o}=e,{listRef:i,activeIndex:c,onMatch:l,onTypingChange:s,enabled:a=!0,findMatch:f=null,resetMs:d=750,ignoreKeys:m=[],selectedIndex:p=null}=t,v=u.useRef(),g=u.useRef(""),h=u.useRef(null!=(n=null!=p?p:c)?n:-1),y=u.useRef(null),b=it(l),E=it(s),w=de(f),x=de(m);return _((()=>{r&&(clearTimeout(v.current),y.current=null,g.current="")}),[r]),_((()=>{var e;r&&""===g.current&&(h.current=null!=(e=null!=p?p:c)?e:-1)}),[r,p,c]),u.useMemo((()=>{if(!a)return{};function e(e){e?o.current.typing||(o.current.typing=e,E(e)):o.current.typing&&(o.current.typing=e,E(e))}function t(e,t,n){const r=w.current?w.current(t,n):t.find((e=>0===(null==e?void 0:e.toLocaleLowerCase().indexOf(n.toLocaleLowerCase()))));return r?e.indexOf(r):-1}function n(n){const o=i.current;if(g.current.length>0&&" "!==g.current[0]&&(-1===t(o,o,g.current)?e(!1):" "===n.key&&T(n)),null==o||x.current.includes(n.key)||1!==n.key.length||n.ctrlKey||n.metaKey||n.altKey)return;r&&" "!==n.key&&(T(n),e(!0));o.every((e=>{var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&g.current===n.key&&(g.current="",h.current=y.current),g.current+=n.key,clearTimeout(v.current),v.current=setTimeout((()=>{g.current="",h.current=y.current,e(!1)}),d);const u=h.current,c=t(o,[...o.slice((u||0)+1),...o.slice(0,(u||0)+1)],g.current);-1!==c?(b(c),y.current=c):" "!==n.key&&(g.current="",e(!1))}return{reference:{onKeyDown:n},floating:{onKeyDown:n,onKeyUp(t){" "===t.key&&e(!1)}}}}),[a,r,o,i,d,x,w,b,E])}}));
