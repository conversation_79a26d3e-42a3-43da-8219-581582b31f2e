"use client";var J=Object.defineProperty;var X=(o,e,t)=>e in o?J(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t;var f=(o,e,t)=>(X(o,typeof e!="symbol"?e+"":e,t),t);import{useFocusRing as $}from"@react-aria/focus";import{useHover as j}from"@react-aria/interactions";import y,{Fragment as E,createContext as G,useContext as O,useEffect as q,useId as z,useMemo as P,useReducer as Q,useRef as U,useSyncExternalStore as Y}from"react";import{useDisposables as Z}from'../../hooks/use-disposables.js';import{useEvent as a}from'../../hooks/use-event.js';import{useSyncRefs as R}from'../../hooks/use-sync-refs.js';import{FloatingProvider as ee,useFloatingPanel as te,useFloatingReference as oe}from'../../internal/floating.js';import{State as V,useOpenClosed as ie}from'../../internal/open-closed.js';import{match as p}from'../../utils/match.js';import{RenderFeatures as N,forwardRefWithAs as I,mergeProps as ne,render as F}from'../../utils/render.js';import{Description as k,useDescribedBy as re,useDescriptions as le}from'../description/description.js';import{Keys as H}from'../keyboard.js';import{Portal as ae}from'../portal/portal.js';var pe=(i=>(i[i.Hidden=0]="Hidden",i[i.Initiated=1]="Initiated",i[i.Visible=2]="Visible",i[i.Hiding=3]="Hiding",i))(pe||{}),se=(t=>(t[t.Delayed=0]="Delayed",t[t.Immediate=1]="Immediate",t))(se||{});class Te{constructor(){f(this,"_state",null);f(this,"_listeners",[]);f(this,"subscribe",e=>(this._listeners.push(e),()=>{this._listeners=this._listeners.filter(t=>t!==e)}));f(this,"getSnapshot",()=>this._state);f(this,"getServerSnapshot",()=>this._state);f(this,"setTooltipId",e=>{this._state!==e&&(this._state=e,this._listeners.forEach(t=>t(e)))})}}let S=new Te;var de=(t=>(t[t.ShowTooltip=0]="ShowTooltip",t[t.HideTooltip=1]="HideTooltip",t))(de||{});let ue={[0](o,e){return{...o,tooltipState:p(o.tooltipState,{[0]:p(e.when,{[1]:2,[0]:1}),[1]:p(e.when,{[1]:2,[0]:1}),[2]:2,[3]:2})}},[1](o,e){return{...o,tooltipState:p(o.tooltipState,{[0]:0,[1]:0,[2]:p(e.when,{[1]:0,[0]:3}),[3]:p(e.when,{[1]:0,[0]:3})})}}},x=G(null);x.displayName="TooltipActionsContext";function B(o){let e=O(x);if(e===null){let t=new Error(`<${o} /> is missing a parent <Tooltip /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,B),t}return e}let W=G(null);W.displayName="TooltipDataContext";function w(o){let e=O(W);if(e===null){let t=new Error(`<${o} /> is missing a parent <Tooltip /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,w),t}return e}function me(o,e){return p(e.type,ue,o,e)}let fe=E;function ce(o,e){let{id:t=`headlessui-tooltip-${z()}`,showDelayMs:s=750,hideDelayMs:i=300,...c}=o,n=Y(S.subscribe,S.getSnapshot,S.getServerSnapshot),[r,d]=Q(me,{id:t,tooltipState:0}),[g,u]=le(),T=Z();q(()=>{T.dispose(),p(r.tooltipState,{[0](){},[1](){T.setTimeout(()=>m(1),s)},[2](){},[3](){T.setTimeout(()=>h(1),i)}})},[T,r.tooltipState,s,i]);let m=a(l=>{l===0&&n!==null&&n!==t&&(l=1),l===1&&S.setTooltipId(t),d({type:0,when:l})}),h=a(l=>{n===t&&l===1&&S.setTooltipId(null),d({type:1,when:l})}),D={ref:R(e)},_=P(()=>({}),[]),v=P(()=>({visible:n===r.id&&p(r.tooltipState,{[0]:!1,[1]:!1,[2]:!0,[3]:!0}),...r}),[n,r]),b=P(()=>({showTooltip:m,hideTooltip:h}),[m,h]);return y.createElement(u,{value:g},y.createElement(ee,null,y.createElement(x.Provider,{value:b},y.createElement(W.Provider,{value:v},F({ourProps:D,theirProps:c,slot:_,defaultTag:fe,name:"Tooltip"})))))}let ge=E;function ye(o,e){var C,M;let{...t}=o,s=w("TooltipTrigger"),i=B("TooltipTrigger"),c=re(),n=U(null),r=R(n,e,oe()),{isFocusVisible:d,focusProps:g}=$({autoFocus:(C=o.autoFocus)!=null?C:!1}),{isHovered:u,hoverProps:T}=j({isDisabled:(M=o.disabled)!=null?M:!1}),m=a(A=>{switch(A.key){case H.Enter:case H.Escape:case H.Space:if(s.tooltipState===2)return i.hideTooltip(1);break}}),h=a(()=>{i.showTooltip(1)}),L=a(()=>{i.hideTooltip(1)}),D=a(()=>{i.hideTooltip(1)}),_=a(()=>{i.showTooltip(0)}),v=a(()=>{i.hideTooltip(0)}),b=a(()=>{s.tooltipState===3&&i.showTooltip(1)}),l=P(()=>{var A;return{hover:u,focus:d,autofocus:(A=o.autoFocus)!=null?A:!1}},[u,d,o.autoFocus]),K=ne({ref:r,"aria-describedby":s.visible?c:void 0,onKeyDown:m,onFocus:h,onBlur:L,onMouseDown:D,onMouseEnter:_,onMouseLeave:v,onMouseMove:b},g,T);return F({ourProps:K,theirProps:t,slot:l,defaultTag:ge,name:"TooltipTrigger"})}let Le=k,he=N.RenderStrategy|N.Static;function Pe(o,e){let{anchor:t={to:"top",padding:8,gap:8,offset:-4},...s}=o,i=w("TooltipPanel"),c=ie(),n=(()=>c!==null?(c&V.Open)===V.Open:i.visible)(),r=U(null),[d,g]=te(n?t:void 0),u=R(r,e,d),T={ref:u,role:"tooltip",...g?{style:g}:{}},m=P(()=>({}),[]);return F({ourProps:{...T,as:E,children:y.createElement(ae,null,y.createElement(k,{ref:u,...s}))},theirProps:{},slot:m,defaultTag:E,features:he,visible:n,name:"TooltipPanel"})}let Ce=I(ce),Me=I(ye),Ge=I(Pe);export{Ce as Tooltip,Ge as TooltipPanel,Me as TooltipTrigger};
