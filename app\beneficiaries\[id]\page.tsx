'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  FileText, 
  Download, 
  Phone, 
  Mail, 
  MapPin,
  Calendar,
  Users,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { getBeneficiaryById, zakatCategoryLabels } from '@/lib/mock-data'
import type { BeneficiaryStatus } from '@/lib/types'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

const statusLabels: Record<BeneficiaryStatus, { ar: string; en: string; variant: any }> = {
  pending_verification: { ar: 'في انتظار التحقق', en: 'Pending Verification', variant: 'secondary' },
  under_review: { ar: 'قيد المراجعة', en: 'Under Review', variant: 'default' },
  approved: { ar: 'موافق عليه', en: 'Approved', variant: 'success' },
  rejected: { ar: 'مرفوض', en: 'Rejected', variant: 'destructive' },
  suspended: { ar: 'معلق', en: 'Suspended', variant: 'warning' },
  inactive: { ar: 'غير نشط', en: 'Inactive', variant: 'outline' }
}

export default function BeneficiaryDetailPage() {
  const { data: session } = useSession() || {}
  const { t, i18n } = useTranslation()
  const params = useParams()
  const beneficiaryId = params.id as string

  if (!session?.user) {
    return null
  }

  // Check if user has access to beneficiary management
  const hasAccess = ['reception_staff', 'researcher', 'department_head', 'admin_manager', 'minister', 'system_admin'].includes(session.user.role)
  
  if (!hasAccess) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">غير مصرح</h2>
            <p className="text-muted-foreground">ليس لديك صلاحية للوصول إلى إدارة المستفيدين</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const beneficiary = getBeneficiaryById(beneficiaryId)

  if (!beneficiary) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">المستفيد غير موجود</h2>
            <p className="text-muted-foreground">لم يتم العثور على المستفيد المطلوب</p>
            <Button asChild className="mt-4">
              <Link href="/beneficiaries">العودة إلى قائمة المستفيدين</Link>
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const StatusBadge = ({ status }: { status: BeneficiaryStatus }) => {
    const label = statusLabels[status]
    return (
      <Badge variant={label.variant}>
        {i18n.language === 'ar' ? label.ar : label.en}
      </Badge>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-6xl">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/beneficiaries">
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة
            </Link>
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight">
                {i18n.language === 'ar' ? beneficiary.fullNameAr : beneficiary.fullNameEn}
              </h1>
              <StatusBadge status={beneficiary.status} />
            </div>
            <p className="text-muted-foreground">
              {i18n.language === 'ar' 
                ? zakatCategoryLabels[beneficiary.primaryCategory].ar 
                : zakatCategoryLabels[beneficiary.primaryCategory].en
              }
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              تعديل
            </Button>
            <Button variant="outline">
              <FileText className="mr-2 h-4 w-4" />
              إدارة الحالة
            </Button>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              إنشاء قسيمة
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">نقاط الأهلية</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{beneficiary.eligibilityScore}/100</div>
              <p className="text-xs text-muted-foreground">
                درجة عالية
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المستلم</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(beneficiary.totalReceived)}</div>
              <p className="text-xs text-muted-foreground">
                {beneficiary.distributionCount} توزيعات
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">حجم الأسرة</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{beneficiary.familySize}</div>
              <p className="text-xs text-muted-foreground">
                {beneficiary.dependents} معالين
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">آخر توزيع</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {beneficiary.lastDistributionDate 
                  ? format(beneficiary.lastDistributionDate, 'dd/MM', {
                      locale: i18n.language === 'ar' ? ar : undefined
                    })
                  : 'لا يوجد'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                {beneficiary.lastDistributionDate 
                  ? format(beneficiary.lastDistributionDate, 'yyyy', {
                      locale: i18n.language === 'ar' ? ar : undefined
                    })
                  : 'لم يتم التوزيع بعد'
                }
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
            <TabsTrigger value="personal">البيانات الشخصية</TabsTrigger>
            <TabsTrigger value="eligibility">الأهلية والتحقق</TabsTrigger>
            <TabsTrigger value="case">إدارة الحالة</TabsTrigger>
            <TabsTrigger value="distribution">سجل التوزيعات</TabsTrigger>
            <TabsTrigger value="family">الأسرة</TabsTrigger>
            <TabsTrigger value="documents">المستندات</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Phone className="h-5 w-5" />
                    معلومات الاتصال
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{beneficiary.phoneNumber}</span>
                  </div>
                  {beneficiary.email && (
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{beneficiary.email}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{beneficiary.address}, {beneficiary.city}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    حالة الحساب
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span>الحالة الحالية:</span>
                    <StatusBadge status={beneficiary.status} />
                  </div>
                  <div className="flex items-center justify-between">
                    <span>تاريخ التسجيل:</span>
                    <span>{format(beneficiary.registrationDate, 'dd/MM/yyyy', {
                      locale: i18n.language === 'ar' ? ar : undefined
                    })}</span>
                  </div>
                  {beneficiary.nextReviewDate && (
                    <div className="flex items-center justify-between">
                      <span>المراجعة القادمة:</span>
                      <span>{format(beneficiary.nextReviewDate, 'dd/MM/yyyy', {
                        locale: i18n.language === 'ar' ? ar : undefined
                      })}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Personal Details Tab */}
          <TabsContent value="personal" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>البيانات الشخصية</CardTitle>
                <CardDescription>
                  المعلومات الأساسية للمستفيد
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="text-sm font-medium">الاسم بالعربية</label>
                    <p className="text-lg">{beneficiary.fullNameAr}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">الاسم بالإنجليزية</label>
                    <p className="text-lg">{beneficiary.fullNameEn}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">رقم الهوية</label>
                    <p className="text-lg font-mono">{beneficiary.nationalId}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">تاريخ الميلاد</label>
                    <p className="text-lg">{format(beneficiary.dateOfBirth, 'dd/MM/yyyy', {
                      locale: i18n.language === 'ar' ? ar : undefined
                    })}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">الجنس</label>
                    <p className="text-lg">{beneficiary.gender === 'male' ? 'ذكر' : 'أنثى'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">الحالة الاجتماعية</label>
                    <p className="text-lg">
                      {beneficiary.maritalStatus === 'married' ? 'متزوج' :
                       beneficiary.maritalStatus === 'single' ? 'أعزب' :
                       beneficiary.maritalStatus === 'divorced' ? 'مطلق' : 'أرمل'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other tabs would be implemented similarly */}
          <TabsContent value="eligibility">
            <Card>
              <CardHeader>
                <CardTitle>الأهلية والتحقق</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">سيتم تطوير هذا القسم قريباً...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="case">
            <Card>
              <CardHeader>
                <CardTitle>إدارة الحالة</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">سيتم تطوير هذا القسم قريباً...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="distribution">
            <Card>
              <CardHeader>
                <CardTitle>سجل التوزيعات</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">سيتم تطوير هذا القسم قريباً...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="family">
            <Card>
              <CardHeader>
                <CardTitle>أفراد الأسرة</CardTitle>
              </CardHeader>
              <CardContent>
                {beneficiary.familyMembers && beneficiary.familyMembers.length > 0 ? (
                  <div className="space-y-3">
                    {beneficiary.familyMembers.map((member) => (
                      <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{member.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {member.relationship} - {member.age} سنة
                          </p>
                        </div>
                        <div className="flex gap-2">
                          {member.isDependent && (
                            <Badge variant="secondary">معال</Badge>
                          )}
                          {member.hasSpecialNeeds && (
                            <Badge variant="outline">احتياجات خاصة</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">لا توجد معلومات عن أفراد الأسرة</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              <CardHeader>
                <CardTitle>المستندات</CardTitle>
              </CardHeader>
              <CardContent>
                {beneficiary.documents && beneficiary.documents.length > 0 ? (
                  <div className="space-y-3">
                    {beneficiary.documents.map((doc) => (
                      <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{doc.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {format(doc.uploadDate, 'dd/MM/yyyy', {
                              locale: i18n.language === 'ar' ? ar : undefined
                            })} - {Math.round(doc.fileSize / 1024)} KB
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          {doc.verified ? (
                            <Badge variant="success">محقق</Badge>
                          ) : (
                            <Badge variant="secondary">في انتظار التحقق</Badge>
                          )}
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">لا توجد مستندات مرفوعة</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
