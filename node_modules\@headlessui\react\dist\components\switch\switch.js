import i,{createContext as R,Fragment as k,useContext as A,useEffect as H,useMemo as E,useRef as F,useState as M}from"react";import{useControllable as U}from'../../hooks/use-controllable.js';import{useDisposables as K}from'../../hooks/use-disposables.js';import{useEvent as m}from'../../hooks/use-event.js';import{useId as B}from'../../hooks/use-id.js';import{useResolveButtonType as I}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as O}from'../../hooks/use-sync-refs.js';import{Features as W,Hidden as N}from'../../internal/hidden.js';import{isDisabledReactIssue7711 as J}from'../../utils/bugs.js';import{attemptSubmit as X}from'../../utils/form.js';import{compact as j,forwardRefWithAs as $,render as _}from'../../utils/render.js';import{Description as q,useDescriptions as z}from'../description/description.js';import{Keys as P}from'../keyboard.js';import{Label as Q,useLabels as V}from'../label/label.js';let b=R(null);b.displayName="GroupContext";let Y=k;function Z(s){var d;let[n,p]=M(null),[c,f]=V(),[r,h]=z(),l=E(()=>({switch:n,setSwitch:p,labelledby:c,describedby:r}),[n,p,c,r]),T={},y=s;return i.createElement(h,{name:"Switch.Description"},i.createElement(f,{name:"Switch.Label",props:{htmlFor:(d=l.switch)==null?void 0:d.id,onClick(t){n&&(t.currentTarget.tagName==="LABEL"&&t.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},i.createElement(b.Provider,{value:l},_({ourProps:T,theirProps:y,defaultTag:Y,name:"Switch.Group"}))))}let ee="button";function te(s,n){let p=B(),{id:c=`headlessui-switch-${p}`,checked:f,defaultChecked:r=!1,onChange:h,name:l,value:T,form:y,...d}=s,t=A(b),u=F(null),g=O(u,n,t===null?null:t.setSwitch),[o,a]=U(f,h,r),S=m(()=>a==null?void 0:a(!o)),D=m(e=>{if(J(e.currentTarget))return e.preventDefault();e.preventDefault(),S()}),C=m(e=>{e.key===P.Space?(e.preventDefault(),S()):e.key===P.Enter&&X(e.currentTarget)}),L=m(e=>e.preventDefault()),x=E(()=>({checked:o}),[o]),v={id:c,ref:g,role:"switch",type:I(s,u),tabIndex:0,"aria-checked":o,"aria-labelledby":t==null?void 0:t.labelledby,"aria-describedby":t==null?void 0:t.describedby,onClick:D,onKeyUp:C,onKeyPress:L},G=K();return H(()=>{var w;let e=(w=u.current)==null?void 0:w.closest("form");e&&r!==void 0&&G.addEventListener(e,"reset",()=>{a(r)})},[u,a]),i.createElement(i.Fragment,null,l!=null&&o&&i.createElement(N,{features:W.Hidden,...j({as:"input",type:"checkbox",hidden:!0,readOnly:!0,form:y,checked:o,name:l,value:T})}),_({ourProps:v,theirProps:d,slot:x,defaultTag:ee,name:"Switch"}))}let ne=$(te),re=Z,we=Object.assign(ne,{Group:re,Label:Q,Description:q});export{we as Switch};
